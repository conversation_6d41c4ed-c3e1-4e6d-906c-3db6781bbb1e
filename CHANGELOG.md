## [1.7.5] - 2025-07-23

### Changed
* Robust history persistance fix

## [1.7.4] - 2025-07-22

### Changed
* Correct partner extraction second time

## [1.7.3] - 2025-07-22

### Changed
* Correct partner extraction

## [1.7.2] - 2025-07-22

### Changed
* History persistance

## [1.7.1] - 2025-07-21

### Changed
* Disable summarizer streaming

## [1.7.0] - 2025-07-17

### Changed
* Clean code
* Add suggestions

## [1.6.0] - 2025-07-16

### Changed
* Rename the cookie name from access-token-ai to access-token

## [1.5.0] - 2025-07-16

### Changed
* Remove options from the connection string because it's not supported by asyncpg driver

## [1.4.0] - 2025-07-16

### Changed
* Add schema of postgres db

## [1.3.0] - 2025-07-16

### Changed
* Add swagger docs

## [1.2.0] - 2025-07-15

### Changed
* add promote_to_app_branch.txt

## [1.1.0] - 2025-07-15

### Changed
* link this project to application release
* change name from ia-agent to ai-agent

## [1.0.0] - 2025-07-03

### Added
* init project
* add basic chainlit app