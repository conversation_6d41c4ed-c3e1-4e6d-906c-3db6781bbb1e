[project]
name = "ai-agent"
version = "0.1.0"
description = "Build Conversational AI."
readme = "README.md"
requires-python = ">=3.13,<4.0.0"
dependencies = [
    "requests>=2.32.3",
    "jwcrypto>=1.5.6",
    "pydantic-settings==2.8.1",
    "python-dateutil==2.9.0.post0",
    "fastapi-health>=0.4.0",
    "urllib3>=2.5.0",
    "httpx>=0.23.0",
    "literalai==0.1.201",
    "dataclasses_json>=0.6.7,<0.7",
    "fastapi>=0.115.3,<0.116",
    "starlette>=0.41.2",
    "python-socketio>=5.11.0,<6",
    "aiofiles>=23.1.0,<25.0.0",
    "syncer>=2.0.3,<3",
    "asyncer>=0.0.8,<0.0.9",
    "mcp>=1.3.0,<2",
    "nest-asyncio>=1.6.0,<2",
    "click>=8.1.3,<9",
    "tomli>=2.0.1,<3",
    "pydantic>=2.7.2,<3",
    "python-dotenv>=1.0.0,<2",
    "uptrace>=1.29.0,<2",
    "watchfiles>=0.20.0,<0.21",
    "filetype>=1.2.0,<2",
    "lazify>=0.4.0,<0.5",
    "packaging>=23.1",
    "python-multipart>=0.0.18,<0.0.19",
    "pyjwt>=2.10.1",
    "uvicorn>=0.34.0",
    "langgraph==0.5.1",
    "langchain==0.3.26",
    "langchain_core==0.3.68",
    "langchain-openai==0.3.27",
    "langchain_deepseek==0.1.3",
    "langchain_google_genai==2.1.6",
    "langchain_anthropic==0.3.17",
    "langchain_litellm==0.2.1",
    "langchain-azure-ai==0.1.4",
    "aiohttp>=3.12.13",
    "plotly==6.0.1",
    "numpy==2.3.1",
    "langgraph-checkpoint-postgres>=2.0.21",
    "asyncpg>=0.30.0",
    "psycopg[binary]>=3.2.9"
]

[project.scripts]
chainlit = "chainlit.cli:cli"

[dependency-groups]
dev = [
    "mypy>=1.15.0",
    "pre-commit>=4.1.0",
    "pytest>=8.3.4",
    "pytest-dotenv>=0.5.2",
    "ruff>=0.9.8",
    "cruft>=2.16.0",
    "pytest-mock==3.14.0",
    "pytest-cov>=6.0.0",
    "httpx>=0.28.1",
]
tests = [
    "pytest>=8.3.2,<9",
    "pytest-asyncio>=0.23.8,<0.24",
    "pytest-cov>=6.0.0",
    "openai>=1.11.1,<2",
    "langchain==0.3.26",
    "llama-index>=0.10.45,<0.11",
    "semantic-kernel>=1.24.0,<2",
    "tenacity>=8.4.1,<9",
    "transformers~=4.38",
    "matplotlib>=3.7.1,<4",
    "plotly==6.0.1",
    "slack_bolt>=1.18.1,<2",
    "discord>=2.3.2,<3",
    "botbuilder-core>=4.15.0,<5",
    "aiosqlite>=0.20.0,<0.21",
    "pandas>=2.2.2,<3",
    "moto>=5.0.14,<6",
]
mypy = [
    "mypy~=1.13",
    "types-requests>=********,<3",
    "types-aiofiles>=********,<24",
    "mypy-boto3-dynamodb>=1.34.113,<2",
    "pandas-stubs>=2.2.2,<3 ; python_version >= '3.9'",
]
custom-data = [
    "asyncpg>=0.30.0,<0.31",
    "SQLAlchemy>=2.0.28,<3",
    "boto3>=1.34.73,<2",
    "azure-identity>=1.14.1,<2",
    "azure-storage-file-datalake>=12.14.0,<13",
    "azure-storage-blob>=12.24.0,<13",
    "google-cloud-storage>=2.19.0,<3",
]

[tool.uv]
default-groups = [
    "tests",
    "dev",
    "mypy",
    "custom-data",
]

[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"


[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".ipynb_checkpoints",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pyenv",
    ".pytest_cache",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    ".vscode",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "site-packages",
    "venv",
    "charts"
]

# Same as Black.
line-length = 140
indent-width = 4

# Assume Python 3.13
target-version = "py313"

# Enable application of unsafe fixes. If excluded, a hint will be displayed when unsafe fixes are available.
unsafe-fixes = true

[tool.ruff.lint]
# Enable Pyflakes (`F`) and a subset of the pycodestyle (`E`)  codes by default.
# Unlike Flake8, Ruff doesn't enable pycodestyle warnings (`W`) or
# McCabe complexity (`C901`) by default.
select = ["ALL"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

ignore-init-module-imports = false

[tool.ruff.lint.per-file-ignores]
"tests/*" = ["S101", "D102", "ANN001", "D101", "D100", "S105", "SLF001", "E501", "ANN201", "ARG002", "D103", "PLR2004", "PLR0913"]
"chainlit/types.py" = ["A005"]
"chainlit/data/client.py" = ["PLR0913"]
"chainlit/utils/utils.py" = ["ANN401"]
"ai_agent/types.py" = ["A005"]
"ai_agent/data/client.py" = ["PLR0913"]
"ai_agent/utils/utils.py" = ["ANN401"]

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

# Enable auto-formatting of code examples in docstrings. Markdown,
# reStructuredText code/literal blocks and doctests are all supported.
#
# This is currently disabled by default, but it is planned for this
# to be opt-out in the future.
docstring-code-format = false

# Set the line length limit used when formatting code snippets in
# docstrings.
#
# This only has an effect when the `docstring-code-format` setting is
# enabled.
docstring-code-line-length = "dynamic"

[tool.pytest.ini_options]
testpaths = ["tests"]
asyncio_mode = "auto"
required_plugins = ["pytest-dotenv"]
env_files = ["tests/.test.env"]
