import fastapi
import pytest
from fastapi import HTTPException

from ai_agent.security.fast_api_security_settings import FastApiSecuritySettings, TokenInfo, get_public_key_web_content


@pytest.fixture
def mock_public_key():
    return [
        {
            "e": "AQ<PERSON>",
            "kid": "dL9RkP57UMJvArNbOVo431ilN0OnTr851Mvsa-nHccI",
            "kty": "RSA",
            "n": "uK0RqOn7jcV-NHmQnSmAkgjUrtS7o_DnI3Wn11Xavm96L13rPuuqhsSfTF_b9teQH0t3Al"
            "-6pfWB0l03OV9iiBbXrR6NLbWKKMoLmTl8RRHF3XMWDk_mGUIfGwwZYym3OtwIjo-U_ca3"
            "GcEnmD_2RAETTpUdL3qPDdm7IMgDWlobvJ49mXBJSs82wvs7Y2tShF-Au8XNBogpAP9Yy8"
            "Rz1QGAuzpREFFbW5qqr2qS7UAMRnlQWi_J283xPFEsRKwMd2GtQPDvwtmbnpNGkW2naKZm"
            "ck5lDjHkv1U2gqnD5yq5lqMG4Dt6tU57FYt__If48_cf2-xk9loTTX3VN2IhLw",
        },
    ]


@pytest.fixture
def mock_public_key_as_file() -> bytes:
    return (
        b"-----BEGIN PUBLIC KEY-----\nMIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAu"
        b"K0RqOn7jcV+NHmQnSmA\nkgjUrtS7o/DnI3Wn11Xavm96L13rPuuqhsSfTF/b9teQH0t3Al+6"
        b"pfWB0l03OV9i\niBbXrR6NLbWKKMoLmTl8RRHF3XMWDk/mGUIfGwwZYym3OtwIjo+U/ca3GcE"
        b"nmD/2\nRAETTpUdL3qPDdm7IMgDWlobvJ49mXBJSs82wvs7Y2tShF+Au8XNBogpAP9Yy8Rz\n1"
        b"QGAuzpREFFbW5qqr2qS7UAMRnlQWi/J283xPFEsRKwMd2GtQPDvwtmbnpNGkW2n\naKZmck5l"
        b"DjHkv1U2gqnD5yq5lqMG4Dt6tU57FYt//If48/cf2+xk9loTTX3VN2Ih\nLwIDAQAB\n-----E"
        b"ND PUBLIC KEY-----\n"
    )


@pytest.fixture
def mock_valid_token() -> str:
    return (
        "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************"
        "VyIjoicGFydG5lcl8xMjMiLCJzY29wZSI6InJlYWQgd3JpdGUiLCJhdWQiOiJhY2NvdW50IiwiZXhwIjozM"
        "zI3Nzg4NDU3NH0.Jik9-CMJqcPuy_hjujXf66qXP56lPjOROehQnLHlUhSkNfq5ipflXOsM2hdczGEDKd5T"
        "Fn9rSkn0jwgKap5aZv9nTSa6OmVp49NNd3gM0OHKwEtaWIT-YvrjgoTbpohb8UJs23AQJkCF5ZYoGV7SOTy"
        "6mABmqjRjlQnlssBircTdCAFlUlXVKnjygE8OfeeB9coMYkxYY-Z9PTFBiBW6XCbJERKmMRZ0wah0QfZ0YJ"
        "6fpi8FF8UFgJ54Wko5OU0qVtBXk-WPq64OyoF5b-O-DlB2HqYI_ibxX_ZGjDc_NPsFVcFZK0vMW6i-ZqlEL"
        "zCPAfJEJZHqghz0yIv-tBbnZQ"
    )


@pytest.fixture
def mock_expired_token() -> str:
    return (
        "eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************"
        "VyIjoicGFydG5lcl8xMjMiLCJzY29wZSI6InJlYWQgd3JpdGUiLCJhdWQiOiJhY2NvdW50IiwiZXhwIjoxN"
        "zQxOTA0MjY1fQ.ZTmMSmYJpfCY2A4Ctyu8dht_3X3aE5BnyshLxO54zMCkQ3cuBWnXZps7L5Lbca-htQyeZ"
        "m9Tdf6YLkuBohqBS4iJMxTFfke-xAxN3MGxr0KU54votGbM17nJLuXn0_AYNqg-nXYEYWgsEqDouUWLNGoi"
        "5b7ReBoYdamZ8tXILfHzBaXeNgvBtL-g16n052tJQSAMVlKB7UmhmJAtx7h_Kw5u0UCCttSsJKFzY5ZxNGb"
        "IgqNNiKEUo5JIVvNgygmqXlWamqO0rYLPIMvonn1ErT7cGhJDRSWI046XpNG_Hz-b66Nv90Kg2Ha18s1K14"
        "ArqzaKuryDIrKFtUAoB41Dnw"
    )


@pytest.fixture
def mock_decoded_token():
    return {
        "email": "<EMAIL>",
        "scope": "read write",
        "partner": "partner_123",
    }


@pytest.fixture
def mock_get(mocker):
    """Fixture to mock requests.get."""
    return mocker.patch("requests.get")


@pytest.fixture
def mock_response(mocker):
    """Fixture to mock response."""
    return mocker.MagicMock()


def test_get_public_key_web_content_valid_url(mock_get, mock_response, mock_public_key) -> None:
    public_key_url = "https://fake-url.com"
    mock_response.status_code = 200
    mock_response.json.return_value = {"keys": mock_public_key}
    mock_get.return_value = mock_response

    # When
    result = get_public_key_web_content(public_key_url)

    # Then
    assert result == mock_public_key
    mock_get.assert_called_once_with(url=public_key_url, timeout=60)


def test_get_public_key_web_content_invalid_key(mock_get, mock_response) -> None:
    public_key_url = "https://fake-url.com"
    mock_response.status_code = 200
    mock_response.json.return_value = {}
    mock_get.return_value = mock_response

    # When
    result = get_public_key_web_content(public_key_url)

    # Then
    assert result is None


def test_get_public_key_web_content_when_url_not_found() -> None:
    # When
    result = get_public_key_web_content(None)

    # Then
    assert result == []


def test_get_public_key_valid(mock_public_key, mock_public_key_as_file) -> None:
    FastApiSecuritySettings.public_key_web_content = mock_public_key

    # When
    result = FastApiSecuritySettings.get_public_key(0)

    # Then
    assert result == mock_public_key_as_file


def test_get_public_key_invalid() -> None:
    FastApiSecuritySettings.public_key_web_content = []

    # When
    with pytest.raises(HTTPException) as exception:
        FastApiSecuritySettings.get_public_key(0)

    # Then
    assert exception.value.status_code == fastapi.status.HTTP_401_UNAUTHORIZED
    assert exception.value.detail == "INVALID_KEY"


def test_get_token_info_when_token_is_valid(mock_public_key, mock_valid_token) -> None:
    FastApiSecuritySettings.public_key_web_content = mock_public_key

    expected_result = TokenInfo(
        email="<EMAIL>",
        partner="partner_123",
        scopes={"read", "write"},
        token=mock_valid_token,
    )

    # When
    token_info = FastApiSecuritySettings.get_token_info(mock_valid_token)

    # Then
    assert token_info == expected_result


def test_get_token_info_when_token_is_invalid(mock_public_key) -> None:
    FastApiSecuritySettings.public_key_web_content = mock_public_key

    # When
    with pytest.raises(HTTPException) as exception:
        FastApiSecuritySettings.get_token_info("fake_token")

    # Then
    assert exception.value.status_code == fastapi.status.HTTP_401_UNAUTHORIZED
    assert exception.value.detail == "INVALID_TOKEN"


def test_get_token_info_when_token_is_expired(mock_public_key, mock_expired_token) -> None:
    FastApiSecuritySettings.public_key_web_content = mock_public_key

    # When
    with pytest.raises(HTTPException) as exception:
        FastApiSecuritySettings.get_token_info(mock_expired_token)

    # Then
    assert exception.value.status_code == fastapi.status.HTTP_401_UNAUTHORIZED
    assert exception.value.detail == "EXPIRED_TOKEN"


def test_has_scopes_info_when_token_has_all_required_scopes(mock_public_key, mock_valid_token) -> None:
    FastApiSecuritySettings.public_key_web_content = mock_public_key

    expected_result = TokenInfo(
        email="<EMAIL>",
        partner="partner_123",
        scopes={"read", "write"},
        token=mock_valid_token,
    )

    # When
    token_info = FastApiSecuritySettings.has_scopes({"read"}, mock_valid_token)

    # Then
    assert token_info == expected_result


def test_has_scopes_info_when_token_lacks_required_scopes(mock_public_key, mock_valid_token) -> None:
    FastApiSecuritySettings.public_key_web_content = mock_public_key

    # When
    with pytest.raises(HTTPException) as exception:
        FastApiSecuritySettings.has_scopes({"other"}, mock_valid_token)

    # Then
    assert exception.value.status_code == fastapi.status.HTTP_403_FORBIDDEN
    assert exception.value.detail == "FORBIDDEN"
