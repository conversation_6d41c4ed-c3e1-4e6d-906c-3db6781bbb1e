# Multi-stage build for Python application with uv
FROM python:3.13-slim-bullseye AS builder
# Install uv
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Install system build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    g++ \
    gcc \
    libffi-dev \
    libssl-dev \
    && rm -rf /var/lib/apt/lists/* \
    && apt-get clean

# Set working directory
WORKDIR /app

# Copy only necessary files for dependency installation first
COPY pyproject.toml uv.lock README.md ./

# Install dependencies
RUN --mount=type=cache,target=/root/.cache/uv \
    uv sync --frozen --no-install-project

# Copy the project into the intermediate image
COPY . .

FROM python:3.13-slim-bullseye

# Set working directory
WORKDIR /app

# Copy application and virtual environment from builder
COPY --from=builder --chown=app:app /app/ /app/

# Set environment variables
ENV PYTHONPATH=/app \
    PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/app/.venv/bin:$PATH"

# Expose port
EXPOSE 8000

# # Run the application
CMD [ "python", "-m", "chainlit", "run", "ai_agent/main.py", "--host", "0.0.0.0", "--port", "8000", "--headless"]
