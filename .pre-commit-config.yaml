# See https://pre-commit.com for more information
# See https://pre-commit.com/hooks.html for more hooks
# exclude: ''
repos:

    -   repo: https://github.com/astral-sh/ruff-pre-commit
        # Ruff version.
        rev: v0.9.7
        hooks:
            # Run the linter.
            -   id: ruff
                args: [., --fix ]
            # Run the formatter.
            -   id: ruff-format

    -   repo: https://github.com/pre-commit/pre-commit-hooks
        rev: v5.0.0
        hooks:
            -   id: check-yaml
                exclude: charts
            -   id: check-added-large-files
            -   id: no-commit-to-branch
                args: ['--branch', '']

    -   repo: https://github.com/pre-commit/mirrors-mypy
        rev: v1.15.0
        hooks:
            -   id: mypy
                verbose: true
                # exclude: ''
                additional_dependencies:
                    - 'fastapi==0.115.12'
                    - 'pydantic==2.10.6'
                    - 'types-requests==2.32.0.20250306'
                    - 'pytest==8.3.5'
                    - 'pyjwt==2.10.1'
                    - 'types-jwcrypto==1.5.0.20241221'
                    - 'pydantic-settings==2.8.1'
                    - 'types-python-dateutil==2.9.0.20241206'
                args: [ --config-file, mypy.ini, --no-incremental]
