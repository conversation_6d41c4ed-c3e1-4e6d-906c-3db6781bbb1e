sonar.host.url=${env.SONAR_HOST_URL}
sonar.token=${env.SONAR_TOKEN}
# defaults to project key
sonar.projectKey=algo.business:ai_agent
sonar.projectName=ai_agent
sonar.projectDescription=Algo_Python
sonar.projectBaseDir=.
sonar.sources=ai_agent
sonar.exclusions=tests/**
sonar.tests=tests
sonar.test.inclusions=tests/**
sonar.python.coverage.reportPaths=coverage.xml
sonar.dependencyCheck.htmlReportPath=dependency-check-report.html
sonar.dependencyCheck.jsonReportPath=dependency-check-report.json
sonar.coverage.dtdVerification=false
sonar.inclusions=ai_agent/**
sonar.plugins.downloadOnlyRequired=false
sonar.buildbreaker.skip=true
sonar.python.version=13.3