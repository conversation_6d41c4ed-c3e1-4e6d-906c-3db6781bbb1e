[mypy]
disallow_untyped_calls = True
disallow_untyped_defs = True
disallow_incomplete_defs = True
check_untyped_defs = True
warn_no_return = True
strict_optional = True
strict_equality = True
no_implicit_optional = True
disallow_any_generics = True
#disallow_any_unimported = True
warn_redundant_casts = True
warn_unused_ignores = True
warn_unused_configs = True
show_traceback = True
show_error_codes = True
#pretty = True
#always_false = MYPYC
python_version = 3.13

plugins=

[mypy-tests.*]
# Add test folder to ignore some of mypy's rules
disallow_untyped_calls = False
disallow_untyped_defs = False
disallow_incomplete_defs = False

[mypy-yaml.*]
ignore_missing_imports = True

[tool.mypy]
namespace_packages = true

[mypy-fastapi_health.*]
ignore_missing_imports = True