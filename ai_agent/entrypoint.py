"""http entrypoint file."""

from typing import Annotated, Any

from fastapi import <PERSON><PERSON><PERSON>
from fastapi.openapi.docs import get_swagger_ui_html
from fastapi.openapi.utils import get_openapi
from fastapi.params import Depends
from fastapi_health import health

from ai_agent.config import settings
from ai_agent.security.fast_api_security_settings import TokenInfo

app = FastAPI(root_path="/ai-agent")
app.add_api_route("/health", health([]))


@app.get("/_hello_world")
def get_hello_world(token_info: Annotated[TokenInfo, Depends(settings.get_token_info)]) -> dict[str, Any]:
    """Hello World"""
    return {
        "email": token_info.get("email"),
        "partner": token_info.get("partner"),
        "message": "Hello World!"
    }


@app.get("/docs", include_in_schema=False)
async def custom_swagger_ui_html():
    return get_swagger_ui_html(
        openapi_url=app.openapi_url,
        title=app.title + " - Swagger UI",
        oauth2_redirect_url=app.swagger_ui_oauth2_redirect_url,
        swagger_js_url="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui-bundle.js",
        swagger_css_url="https://unpkg.com/swagger-ui-dist@4.15.5/swagger-ui.css",
    )
