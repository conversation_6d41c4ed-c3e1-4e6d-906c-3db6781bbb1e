import os
import uuid
import json
import asyncio
from typing import Dict, List, Any, Optional, TypedDict, Annotated
from dataclasses import dataclass
from operator import or_, add

# <PERSON><PERSON><PERSON><PERSON> and LangGraph imports
from langchain_core.messages import HumanMessage
from langchain_core.prompts import Chat<PERSON>romptTemplate
from langgraph.prebuilt import create_react_agent
from langgraph.checkpoint.postgres.aio import AsyncPostgresSaver
from langgraph.graph.message import add_messages
from langgraph.graph import END, StateGraph

# Model and tool imports
from ai_agent.model import model, summary_model, get_prompt_for_partner
from ai_agent.tools import complete_python_task, data_retriever
from ai_agent.summary import pre_model_hook


# Database config (use environment variables)
DB_HOST = os.getenv("POSTGRES_HOST", "localhost")
DB_PORT = os.getenv("POSTGRES_PORT", "5432")
DB_NAME = os.getenv("POSTGRES_DB", "chainlit_db")
DB_USER = os.getenv("POSTGRES_USER", "user_test")
DB_PASSWORD = os.getenv("POSTGRES_PASSWORD", "password_test")
DB_URI_CHECKPOINTER = (
    f"postgresql://{DB_USER}:{DB_PASSWORD}@{DB_HOST}:{DB_PORT}/{DB_NAME}?sslmode=disable"
)

# ─────────────────────────────────────────────────────────────────────────────
# Data structures
@dataclass
class InputData:
    variable_name: str
    data_path: str
    data_description: str
    sandbox_file_name: str
    def __hash__(self):
        return hash((self.variable_name, self.data_path, self.data_description,self.sandbox_file_name))

class AgentState(TypedDict):
    messages: Annotated[List[Any], add_messages]
    remaining_steps: int
    input_data: Annotated[List[InputData], add]
    intermediate_outputs: Annotated[List[dict], add]
    current_variables: Annotated[Dict[str, Any], or_]
    output_image_paths: Annotated[List[str], add]
    data_description: Annotated[List[str], add]
    generic_parser_request: Annotated[List[Any], add]
    conversation_id: str
    session_id: str
    partner: str
    partner_config: Optional[Dict[str, Any]]
    summary: str
    id_last_summary: Optional[str]
    suggestions: List[str]

# ─────────────────────────────────────────────────────────────────────────────
# Utility: shallow merge of two dicts

def _merge(a: Dict[str, Any], b: Dict[str, Any]) -> Dict[str, Any]:
    return {**a, **b}

# ─────────────────────────────────────────────────────────────────────────────
# Checkpointer factory

def make_postgres_checkpointer():
    """
    Return an async context manager for AsyncPostgresSaver.
    Usage:
        async with make_postgres_checkpointer() as ckpt:
            ...
    """
    return AsyncPostgresSaver.from_conn_string(DB_URI_CHECKPOINTER)

tools = [data_retriever, complete_python_task]
# ─────────────────────────────────────────────────────────────────────────────
# Agent factory

def create_agent(checkpointer, partner: str):
    """
    Return a LangGraph React agent with the given async checkpointer.
    """
    prompt = ChatPromptTemplate.from_messages([
        ("system", get_prompt_for_partner()),
        ("placeholder", "{messages}"),
    ])

    agent = create_react_agent(
        model=model,
        tools=tools,
        prompt=prompt,
        pre_model_hook=pre_model_hook,
        state_schema=AgentState,
        checkpointer=checkpointer,
    )
    return agent

def create_agent_subgraph(checkpointer, partner: str):
    """
    Create the agent as a subgraph (your original agent logic)
    """
    prompt = ChatPromptTemplate.from_messages([
        ("system", get_prompt_for_partner()),
        ("placeholder", "{messages}"),
    ])

    agent = create_react_agent(
        model=model,
        tools=tools,
        prompt=prompt,
        pre_model_hook=pre_model_hook,
        state_schema=AgentState,
        checkpointer=checkpointer,
    )
    return agent

# ─────────────────────────────────────────────────────────────────────────────
# Suggestions node
from typing import Dict

async def generate_suggestions_node(state: AgentState) -> Dict[str, Any]:
    """
    Generate 3 suggestions for next questions based on last 3 messages, summary, and available data
    """
    # Get last 3 messages
    last_messages = state["messages"][-3:] if len(state["messages"]) >= 3 else state["messages"]
    
    # Format messages for the prompt
    message_context = ""
    for msg in last_messages:
        if hasattr(msg, 'content'):
            role = "Human" if isinstance(msg, HumanMessage) else "Assistant"
            message_context += f"{role}: {msg.content}\n"
    
    # Include summary if it exists
    summary_context = ""
    if state.get("summary"):
        summary_context = f"Conversation Summary: {state['summary']}\n\n"

    # Add available data descriptions from input_data
    input_data = state.get("input_data", [])
    data_descriptions = []
    for d in input_data:
        desc = getattr(d, 'data_description', None) or (d.get('data_description') if isinstance(d, dict) else None)
        if desc:
            data_descriptions.append(str(desc))
    data_context = ""
    if data_descriptions:
        data_context = "Available Data:\n" + "\n".join(f"- {desc}" for desc in data_descriptions) + "\n\n"
    
    # Create prompt for suggestions
    suggestions_prompt = f"""Vous êtes un·e assistant·e expert·e en analyse énergétique. À partir des éléments ci‑dessous, anticipez ce dont l’utilisateur aura besoin ensuite et rédigez **exactement trois** questions de suivi courtes.

Contexte (à lire seulement — NE PAS citer) :
{summary_context}
{data_context}

Conversation récente (à lire seulement — NE PAS citer) :
{message_context}

Règles :
• Formulez chaque question à la première personne, dans la voix de l’utilisateur (« Je voudrais… », « J’ai besoin de… »).
• Gardez chaque question concise, exploitable et **strictement liée** à ce que l’utilisateur a déjà dit.
• **Règle sur la profondeur de la conversation** :
  – Si l’utilisateur n’a fait qu’un salut ou n’a donné aucun détail technique, fournissez *exactement* ces trois questions de départ (une par ligne, texte exact) :
    montre‑moi mes 10 sites ayant la plus grande superficie
    j’ai besoin de voir mes données de consommation mensuelle de l’année dernière
    comment peux‑tu m’aider
  – Sinon, rédigez trois questions spécifiques au contexte, sans introduire de KPI, d’analyse avancée ou de termes inconnus sauf s’ils ont déjà été mentionnés.
• N’incluez jamais de demandes de code, d’outils ou d’implémentation.
• Évitez toute formulation générique ou répétitive.
• **Toutes les questions doivent être en français.**

Format de sortie :
Renvoyez exactement trois questions — une par ligne, sans puces ni numérotation.
"""

    # Get suggestions from the model
    try:
        response = await summary_model.ainvoke([HumanMessage(content=suggestions_prompt)])
        suggestions_text = response.content
        
        # Parse the suggestions (split by lines and clean up)
        suggestions = [
            line.strip() 
            for line in suggestions_text.split('\n') 
            if line.strip() and not line.strip().startswith(('1.', '2.', '3.', '-', '*'))
        ]
        # Deduplicate and filter
        seen = set()
        filtered = []
        for s in suggestions:
            if s.lower() not in seen and 'explore next' not in s.lower():
                filtered.append(s)
                seen.add(s.lower())
        suggestions = filtered[:3]
        while len(suggestions) < 3:
            suggestions.append("What would you like to ask next?")
        print(f"[SUGGESTIONS]:{suggestions}")
        
    except Exception as e:
        print(f"Error generating suggestions: {e}")
        # Fallback suggestions
        suggestions = [
            "Can you help me with something else?",
            "What other options do I have?",
            "How can we improve this further?"
        ]
    
    # Return the full state with updated suggestions
    return {**state, "suggestions": suggestions}


# ─────────────────────────────────────────────────────────────────────────────
# Main graph factory - FIXED VERSION

def create_main_graph(checkpointer, partner: str):
    """
    Create the main graph with agent as subgraph and suggestions generation
    """
    # Create the agent subgraph
    agent_subgraph = create_agent_subgraph(checkpointer, partner)
    
    # Create the main graph
    main_graph = StateGraph(AgentState)
    
    # Add the agent as a subgraph node
    main_graph.add_node("agent", agent_subgraph)
    
    # Add the suggestions generation node
    main_graph.add_node("generate_suggestions", generate_suggestions_node)
    
    # Define the flow: input -> agent -> suggestions -> end
    main_graph.set_entry_point("agent")
    main_graph.add_edge("agent", "generate_suggestions")
    main_graph.add_edge("generate_suggestions", END)
    
    # Compile the graph
    compiled_graph = main_graph.compile(checkpointer=checkpointer)
    
    return compiled_graph





