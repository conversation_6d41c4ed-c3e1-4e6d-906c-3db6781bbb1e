import plotly.graph_objects as go
import plotly.io as pio
import plotly.express as px
import numpy as np
from typing import Union, List, Dict, Optional, Any

# ------------------------------------------------------------------
# Enhanced Brand Palette with Better Contrast and Accessibility
# ------------------------------------------------------------------
COLORS = {
    # Primary backgrounds
    "bg_primary": "#ffffff",      # Main background (white for better contrast)
    "bg_secondary": "#f8faf8",    # Secondary background
    "bg_dark": "#00211b",         # Dark background for headers/emphasis
    "bg_card": "#ffffff",         # Card backgrounds
    
    # Grid and borders
    "grid_primary": "#e0e0e0",    # Primary grid lines
    "grid_secondary": "#f0f0f0",  # Secondary/subtle grid
    "border": "#d0d0d0",          # Border color
    
    # Enhanced green palette with better contrast
    "green_950": "#0a3d0c",       # Darkest
    "green_900": "#1b5e20",
    "green_800": "#2e7d32",
    "green_700": "#388e3c",
    "green_600": "#43a047",
    "green_500": "#4caf50",       # Primary brand color
    "green_400": "#66bb6a",
    "green_300": "#81c784",
    "green_200": "#a5d6a7",
    "green_100": "#c8e6c9",
    "green_50": "#e8f5e9",
    
    # Accent colors for variety
    "accent_blue": "#2196f3",
    "accent_orange": "#ff9800",
    "accent_red": "#f44336",
    "accent_purple": "#9c27b0",
    "accent_teal": "#009688",
    
    # Text colors
    "text_primary": "#212121",    # Main text
    "text_secondary": "#616161",  # Secondary text
    "text_light": "#9e9e9e",      # Light text
    "text_inverse": "#ffffff",    # Text on dark backgrounds
}

# Enhanced color sequences for better visual distinction
COLOR_SEQUENCE_PRIMARY = [
    COLORS["green_500"],
    COLORS["accent_blue"],
    COLORS["accent_orange"],
    COLORS["green_700"],
    COLORS["accent_purple"],
    COLORS["green_300"],
    COLORS["accent_teal"],
    COLORS["green_800"],
    COLORS["accent_red"],
    COLORS["green_200"],
]

COLOR_SEQUENCE_MONOCHROME = [
    COLORS["green_300"],
    COLORS["green_500"],
    COLORS["green_700"],
    COLORS["green_200"],
    COLORS["green_800"],
    COLORS["green_400"],
    COLORS["green_600"],
    COLORS["green_900"],
    COLORS["green_100"],
]

# Enhanced sequential colorscale with smoother transitions
SEQUENTIAL_COLORSCALE = [
    [0.0, COLORS["green_50"]],
    [0.1, COLORS["green_100"]],
    [0.25, COLORS["green_200"]],
    [0.4, COLORS["green_300"]],
    [0.55, COLORS["green_400"]],
    [0.7, COLORS["green_500"]],
    [0.85, COLORS["green_700"]],
    [1.0, COLORS["green_900"]],
]

# Diverging colorscale for better data representation
DIVERGING_COLORSCALE = [
    [0.0, COLORS["accent_red"]],
    [0.25, "#ffcdd2"],
    [0.5, "#ffffff"],
    [0.75, COLORS["green_100"]],
    [1.0, COLORS["green_700"]],
]

# ------------------------------------------------------------------
# Typography and Spacing Configuration
# ------------------------------------------------------------------
FONT_FAMILY = "'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif"
FONT_SIZE_BASE = 13
FONT_SIZE_SMALL = 11
FONT_SIZE_TITLE = 20
FONT_SIZE_SUBTITLE = 16
FONT_SIZE_AXIS = 12

# Spacing and sizing
MARGIN_DEFAULT = dict(l=80, r=60, t=80, b=60)
MARGIN_COMPACT = dict(l=60, r=40, t=60, b=40)
CORNER_RADIUS = 4  # For rounded corners where applicable

# ------------------------------------------------------------------
# Create Enhanced Universal Template
# ------------------------------------------------------------------
def create_energisme_template():
    """
    Creates an enhanced universal Plotly template for Energisme with improved aesthetics.
    """
    template = go.layout.Template()
    
    # --- Enhanced Layout Configuration ---
    template.layout = dict(
        # Typography
        font=dict(
            family=FONT_FAMILY, 
            size=FONT_SIZE_BASE, 
            color=COLORS["text_primary"]
        ),
        
        # Title styling with better hierarchy
        title=dict(
            font=dict(
                size=FONT_SIZE_TITLE, 
                color=COLORS["text_primary"],
                family=FONT_FAMILY
            ),
            x=0.5,  # Center aligned
            xanchor='center',
            y=0.98,
            yanchor='top',
            pad=dict(t=0, b=20)
        ),
        
        # Clean backgrounds
        paper_bgcolor=COLORS["bg_primary"],
        plot_bgcolor=COLORS["bg_secondary"],
        
        # Color sequences
        colorway=COLOR_SEQUENCE_PRIMARY,
        colorscale=dict(
            sequential=SEQUENTIAL_COLORSCALE,
            diverging=DIVERGING_COLORSCALE
        ),
        
        # Margins
        margin=MARGIN_DEFAULT,
        
        # Bar chart specific
        bargap=0.2,
        bargroupgap=0.1,
        
        # Enhanced axes styling
        xaxis=dict(
            # Grid
            showgrid=True,
            gridcolor=COLORS["grid_secondary"],
            gridwidth=1,
            griddash='dot',
            
            # Zero line
            zeroline=True,
            zerolinecolor=COLORS["grid_primary"],
            zerolinewidth=1,
            
            # Axis line
            showline=True,
            linecolor=COLORS["border"],
            linewidth=1,
            
            # Ticks
            ticks='outside',
            ticklen=5,
            tickwidth=1,
            tickcolor=COLORS["border"],
            tickfont=dict(
                size=FONT_SIZE_AXIS,
                color=COLORS["text_secondary"]
            ),
            
            # Title
            title_font=dict(
                size=FONT_SIZE_SUBTITLE,
                color=COLORS["text_primary"]
            ),
            
            # Spike lines for better interactivity
            showspikes=True,
            spikecolor=COLORS["grid_primary"],
            spikethickness=1,
            spikedash='dot',
            spikemode='across',
        ),
        
        yaxis=dict(
            # Same as xaxis but with horizontal grid more prominent
            showgrid=True,
            gridcolor=COLORS["grid_primary"],
            gridwidth=1,
            
            zeroline=True,
            zerolinecolor=COLORS["border"],
            zerolinewidth=1,
            
            showline=True,
            linecolor=COLORS["border"],
            linewidth=1,
            
            ticks='outside',
            ticklen=5,
            tickwidth=1,
            tickcolor=COLORS["border"],
            tickfont=dict(
                size=FONT_SIZE_AXIS,
                color=COLORS["text_secondary"]
            ),
            
            title_font=dict(
                size=FONT_SIZE_SUBTITLE,
                color=COLORS["text_primary"]
            ),
            
            showspikes=True,
            spikecolor=COLORS["grid_primary"],
            spikethickness=1,
            spikedash='dot',
            spikemode='across',
        ),
        
        # Enhanced legend
        legend=dict(
            bgcolor='rgba(255, 255, 255, 0.95)',
            bordercolor=COLORS["border"],
            borderwidth=1,
            font=dict(
                size=FONT_SIZE_SMALL,
                color=COLORS["text_primary"]
            ),
            orientation="h",
            yanchor="bottom",
            y=1.02,
            xanchor="left",
            x=0,
            itemsizing='constant',
            itemwidth=30,
            tracegroupgap=5,
            traceorder='normal',
        ),
        
        # Annotations
        annotationdefaults=dict(
            font=dict(
                size=FONT_SIZE_SMALL,
                color=COLORS["text_primary"]
            ),
            arrowcolor=COLORS["text_secondary"],
            arrowsize=1,
            arrowwidth=1.5,
            arrowhead=2,
            bgcolor='rgba(255, 255, 255, 0.9)',
            bordercolor=COLORS["border"],
            borderwidth=1,
            borderpad=4,
        ),
        
        # Enhanced hover labels
        hoverlabel=dict(
            bgcolor=COLORS["bg_dark"],
            bordercolor=COLORS["green_500"],
            font=dict(
                size=FONT_SIZE_SMALL,
                color=COLORS["text_inverse"],
                family=FONT_FAMILY
            ),
            align='left',
        ),
        
        # Selection tools
        selectdirection='any',
        dragmode='zoom',
        
        # Modebar customization
        modebar=dict(
            bgcolor='rgba(255, 255, 255, 0.9)',
            color=COLORS["text_secondary"],
            activecolor=COLORS["green_500"],
        ),
    )
    
    # --- Enhanced Trace Defaults ---
    
    # Bar charts with subtle shadows
    template.data.bar = [go.Bar(
        marker=dict(
            color=COLORS["green_500"],
            line=dict(
                color=COLORS["green_700"],
                width=0
            ),
            opacity=0.9,
            pattern=dict(
                shape="",  # Can be "", "/", "\", "x", "-", "|", "+", "."
            )
        ),
        textfont=dict(
            family=FONT_FAMILY,
            size=FONT_SIZE_SMALL,
            color=COLORS["text_primary"]
        ),
        textposition='auto',
        hovertemplate='<b>%{x}</b><br>%{y}<extra></extra>',
    )]
    
    # Line charts with smooth curves
    template.data.scatter = [go.Scatter(
        line=dict(
            color=COLORS["green_500"],
            width=3,
            shape='spline',  # Smooth lines
            smoothing=0.3,
        ),
        mode="lines+markers",
        marker=dict(
            size=8,
            color=COLORS["green_500"],
            line=dict(
                color=COLORS["bg_primary"],
                width=2
            ),
            symbol='circle',
        ),
        hovertemplate='<b>%{x}</b><br>%{y}<extra></extra>',
    )]
    
    # Enhanced scatter plots
    template.data.scattergl = [go.Scattergl(
        marker=dict(
            size=10,
            color=COLORS["green_500"],
            line=dict(
                color=COLORS["green_700"],
                width=1
            ),
            opacity=0.8,
            symbol='circle',
        ),
        mode="markers",
        hovertemplate='<b>X:</b> %{x}<br><b>Y:</b> %{y}<extra></extra>',
    )]
    
    # Beautiful pie charts
    template.data.pie = [go.Pie(
        marker=dict(
            colors=COLOR_SEQUENCE_PRIMARY,
            line=dict(
                color=COLORS["bg_primary"],
                width=2
            )
        ),
        textfont=dict(
            family=FONT_FAMILY,
            size=FONT_SIZE_SMALL,
            color=COLORS["text_primary"]
        ),
        textposition='auto',
        textinfo='label+percent',
        hovertemplate='<b>%{label}</b><br>%{value}<br>%{percent}<extra></extra>',
        hole=0,  # Set to 0.4 for donut charts
        pull=[0.05 if i == 0 else 0 for i in range(10)],  # Slightly pull out first slice
    )]
    
    # Box plots with better styling
    template.data.box = [go.Box(
        marker=dict(
            color=COLORS["green_500"],
            size=8,
            line=dict(
                color=COLORS["green_700"],
                width=1
            ),
            opacity=0.8,
        ),
        line=dict(
            color=COLORS["green_700"],
            width=2
        ),
        fillcolor=COLORS["green_100"],
        boxmean='sd',  # Show mean and standard deviation
        notched=True,
        whiskerwidth=0.5,
    )]
    
    # Heatmaps with better color scaling
    template.data.heatmap = [go.Heatmap(
        colorscale=SEQUENTIAL_COLORSCALE,
        colorbar=dict(
            thickness=15,
            len=0.7,
            bgcolor=COLORS["bg_primary"],
            bordercolor=COLORS["border"],
            borderwidth=1,
            tickfont=dict(
                size=FONT_SIZE_SMALL,
                color=COLORS["text_secondary"]
            ),
            title=dict(
                font=dict(
                    size=FONT_SIZE_SMALL,
                    color=COLORS["text_primary"]
                ),
                side='right'
            )
        ),
        hovertemplate='X: %{x}<br>Y: %{y}<br>Value: %{z}<extra></extra>',
    )]
    
    # Histograms with better binning
    template.data.histogram = [go.Histogram(
        marker=dict(
            color=COLORS["green_400"],
            line=dict(
                color=COLORS["green_700"],
                width=1
            ),
            opacity=0.85,
            pattern=dict(shape="")
        ),
        nbinsx=20,  # Default number of bins
        histnorm='',  # Can be '', 'percent', 'probability', 'density', 'probability density'
        hovertemplate='Range: %{x}<br>Count: %{y}<extra></extra>',
    )]
    
    # 3D Scatter with better depth perception
    template.data.scatter3d = [go.Scatter3d(
        marker=dict(
            size=8,
            color=COLORS["green_500"],
            colorscale=SEQUENTIAL_COLORSCALE,
            line=dict(
                color=COLORS["green_700"],
                width=0.5
            ),
            opacity=0.8,
            symbol='circle',
        ),
        line=dict(
            color=COLORS["green_500"],
            width=4
        ),
        projection=dict(
            x=dict(show=True, opacity=0.7, scale=0.4),
            y=dict(show=True, opacity=0.7, scale=0.4),
            z=dict(show=True, opacity=0.7, scale=0.4)
        ),
    )]
    
    # Surface plots with better lighting
    template.data.surface = [go.Surface(
        colorscale=SEQUENTIAL_COLORSCALE,
        contours=dict(
            x=dict(show=True, color=COLORS["grid_primary"], width=1),
            y=dict(show=True, color=COLORS["grid_primary"], width=1),
            z=dict(show=True, color=COLORS["grid_primary"], width=1)
        ),
        lighting=dict(
            ambient=0.5,
            diffuse=0.6,
            fresnel=0.4,
            specular=0.2,
            roughness=0.5
        ),
        lightposition=dict(x=-1000, y=1000, z=1000),
    )]
    
    # Violin plots with enhanced styling
    template.data.violin = [go.Violin(
        marker=dict(
            color=COLORS["green_500"],
            line=dict(
                color=COLORS["green_700"],
                width=1
            ),
            opacity=0.8,
        ),
        line=dict(
            color=COLORS["green_700"],
            width=2
        ),
        fillcolor=COLORS["green_100"],
        opacity=0.7,
        box_visible=True,
        meanline_visible=True,
        points='all',
        jitter=0.05,
        scalemode='width',
    )]
    
    # Funnel charts with gradient effect
    template.data.funnel = [go.Funnel(
        marker=dict(
            colorscale=SEQUENTIAL_COLORSCALE,
            line=dict(
                color=COLORS["bg_primary"],
                width=2
            )
        ),
        textfont=dict(
            family=FONT_FAMILY,
            size=FONT_SIZE_SMALL,
            color=COLORS["text_inverse"]
        ),
        textposition='inside',
        textinfo='value+percent initial',
        opacity=0.9,
        connector=dict(
            line=dict(
                color=COLORS["grid_primary"],
                width=2,
                dash='dot'
            )
        ),
    )]
    
    # Waterfall charts with clear connectors
    template.data.waterfall = [go.Waterfall(
        connector=dict(
            line=dict(
                color=COLORS["grid_primary"],
                width=2,
                dash='dot'
            )
        ),
        increasing=dict(
            marker=dict(
                color=COLORS["green_500"],
                line=dict(
                    color=COLORS["green_700"],
                    width=1
                )
            )
        ),
        decreasing=dict(
            marker=dict(
                color=COLORS["accent_red"],
                line=dict(
                    color='darkred',
                    width=1
                )
            )
        ),
        totals=dict(
            marker=dict(
                color=COLORS["green_700"],
                line=dict(
                    color=COLORS["green_900"],
                    width=1
                )
            )
        ),
        textfont=dict(
            family=FONT_FAMILY,
            size=FONT_SIZE_SMALL,
            color=COLORS["text_primary"]
        ),
    )]
    
    # Indicator/gauge charts with modern styling
    template.data.indicator = [go.Indicator(
        gauge=dict(
            axis=dict(
                tickcolor=COLORS["text_secondary"],
                tickfont=dict(
                    size=FONT_SIZE_SMALL,
                    color=COLORS["text_secondary"]
                ),
                range=[None, None],
                tickwidth=1,
            ),
            bar=dict(
                color=COLORS["green_500"],
                thickness=0.8,
            ),
            bgcolor=COLORS["green_50"],
            bordercolor=COLORS["green_300"],
            borderwidth=2,
            steps=[
                dict(range=[0, 50], color=COLORS["green_100"]),
                dict(range=[50, 80], color=COLORS["green_200"]),
                dict(range=[80, 100], color=COLORS["green_300"])
            ],
            threshold=dict(
                line=dict(color=COLORS["accent_red"], width=4),
                thickness=0.75,
                value=90
            )
        ),
        number=dict(
            font=dict(
                color=COLORS["green_700"],
                size=FONT_SIZE_TITLE * 1.5,
                family=FONT_FAMILY
            ),
            suffix="",
            valueformat=".1f"
        ),
        title=dict(
            font=dict(
                color=COLORS["text_primary"],
                size=FONT_SIZE_SUBTITLE,
                family=FONT_FAMILY
            )
        ),
        mode="gauge+number+delta",
        delta=dict(
            reference=50,
            font=dict(size=FONT_SIZE_SUBTITLE)
        ),
    )]
    
    # Treemap with hierarchical colors
    template.data.treemap = [go.Treemap(
        marker=dict(
            colorscale=SEQUENTIAL_COLORSCALE,
            line=dict(
                color=COLORS["bg_primary"],
                width=2
            ),
            pad=dict(t=25, l=2, r=2, b=2)
        ),
        textfont=dict(
            family=FONT_FAMILY,
            size=FONT_SIZE_SMALL,
            color=COLORS["text_inverse"]
        ),
        textposition='middle center',
        pathbar=dict(
            visible=True,
            thickness=20,
            textfont=dict(
                size=FONT_SIZE_SMALL,
                family=FONT_FAMILY
            )
        ),
    )]
    
    # Sunburst with radial gradient effect
    template.data.sunburst = [go.Sunburst(
        marker=dict(
            colorscale=SEQUENTIAL_COLORSCALE,
            line=dict(
                color=COLORS["bg_primary"],
                width=2
            )
        ),
        textfont=dict(
            family=FONT_FAMILY,
            size=FONT_SIZE_SMALL,
            color=COLORS["text_inverse"]
        ),
        insidetextorientation='radial',
        leaf=dict(opacity=0.9),
    )]
    
    # Sankey diagrams with flow visualization
    template.data.sankey = [go.Sankey(
        node=dict(
            pad=20,
            thickness=25,
            line=dict(
                color=COLORS["green_700"],
                width=1
            ),
            color=COLORS["green_400"],
            hoverlabel=dict(
                font=dict(
                    family=FONT_FAMILY,
                    size=FONT_SIZE_SMALL,
                    color=COLORS["text_primary"]
                )
            )
        ),
        link=dict(
            arrowlen=15,
            color='rgba(76, 175, 80, 0.4)',  # Semi-transparent green
            hovertemplate='%{source.label} → %{target.label}<br>%{value}<extra></extra>',
        ),
    )]
    
    # Parallel coordinates with better interactivity
    template.data.parcoords = [go.Parcoords(
        line=dict(
            color=COLORS["green_500"],
            colorscale=SEQUENTIAL_COLORSCALE,
            showscale=True,
            cmin=0,
            cmax=1,
        ),
        labelfont=dict(
            size=FONT_SIZE_SMALL,
            color=COLORS["text_primary"],
            family=FONT_FAMILY
        ),
        tickfont=dict(
            size=FONT_SIZE_SMALL,
            color=COLORS["text_secondary"],
            family=FONT_FAMILY
        ),
        rangefont=dict(
            size=FONT_SIZE_SMALL,
            color=COLORS["text_secondary"],
            family=FONT_FAMILY
        ),
    )]
    
    # Candlestick charts for financial data
    template.data.candlestick = [go.Candlestick(
        increasing=dict(
            line=dict(
                color=COLORS["green_500"],
                width=1
            ),
            fillcolor=COLORS["green_500"]
        ),
        decreasing=dict(
            line=dict(
                color=COLORS["accent_red"],
                width=1
            ),
            fillcolor=COLORS["accent_red"]
        ),
        line=dict(width=1),
        whiskerwidth=0,
    )]
    
    # Area charts with gradient fill
    template.data.scatter = [go.Scatter(
        mode="lines",
        line=dict(
            color=COLORS["green_500"],
            width=2,
            shape='spline',
            smoothing=0.3
        ),
        fillcolor='rgba(76, 175, 80, 0.3)',
        fill="tozeroy",
        hoveron='points+fills',
        hovertemplate='%{x}<br>%{y}<extra></extra>',
    )]
    
    # Table styling
    template.data.table = [go.Table(
        header=dict(
            values=[],
            fill=dict(color=COLORS["green_700"]),
            align=['left', 'center'],
            font=dict(
                color=COLORS["text_inverse"],
                size=FONT_SIZE_BASE,
                family=FONT_FAMILY
            ),
            line=dict(
                color=COLORS["bg_primary"],
                width=1
            ),
            height=35
        ),
        cells=dict(
            values=[],
            fill=dict(
                color=[COLORS["bg_secondary"], COLORS["bg_primary"]]
            ),
            align=['left', 'center'],
            font=dict(
                color=COLORS["text_primary"],
                size=FONT_SIZE_SMALL,
                family=FONT_FAMILY
            ),
            line=dict(
                color=COLORS["grid_primary"],
                width=1
            ),
            height=30
        ),
    )]
    
    # Contour plots with topographic feel
    template.data.contour = [go.Contour(
        colorscale=SEQUENTIAL_COLORSCALE,
        line=dict(
            smoothing=0.85,
            width=1
        ),
        contours=dict(
            showlabels=True,
            labelfont=dict(
                size=FONT_SIZE_SMALL,
                color=COLORS["text_primary"],
                family=FONT_FAMILY
            ),
            labelformat='.2f'
        ),
        colorbar=dict(
            thickness=15,
            len=0.7,
            bgcolor=COLORS["bg_primary"],
            bordercolor=COLORS["border"],
            borderwidth=1,
        ),
    )]
    
    # Histogram2D with better color mapping
    template.data.histogram2d = [go.Histogram2d(
        colorscale=SEQUENTIAL_COLORSCALE,
        colorbar=dict(
            thickness=15,
            len=0.7,
            bgcolor=COLORS["bg_primary"],
            bordercolor=COLORS["border"],
            borderwidth=1,
        ),
        xbins=dict(size=None),
        ybins=dict(size=None),
        hovertemplate='X: %{x}<br>Y: %{y}<br>Count: %{z}<extra></extra>',
    )]
    
    # Polar scatter with radial grid
    template.data.scatterpolar = [go.Scatterpolar(
        line=dict(
            color=COLORS["green_500"],
            width=2,
            shape='spline',
            smoothing=0.3
        ),
        marker=dict(
            size=8,
            color=COLORS["green_500"],
            line=dict(
                color=COLORS["bg_primary"],
                width=1
            ),
            symbol='circle'
        ),
        fill='toself',
        fillcolor='rgba(76, 175, 80, 0.2)',
        hoveron='points+fills',
    )]
    
    return template

# ------------------------------------------------------------------
# Register the template
# ------------------------------------------------------------------
energisme_template = create_energisme_template()
pio.templates["energisme"] = energisme_template
pio.templates.default = "energisme"

# ------------------------------------------------------------------
# Helper Functions for Enhanced Styling
# ------------------------------------------------------------------

def apply_company_style(fig: go.Figure, 
                       style_preset: str = "default",
                       color_sequence: Optional[List[str]] = None) -> go.Figure:
    """
    Apply Energisme styling to an existing figure with additional customization options.
    
    Args:
        fig: Plotly figure to style
        style_preset: One of 'default', 'monochrome', 'vibrant', 'dark'
        color_sequence: Custom color sequence to use
        
    Returns:
        Styled figure
    """
    # Apply base template
    fig.update_layout(template="energisme+ggplot2")
    
    # Apply style presets
    if style_preset == "monochrome":
        color_seq = COLOR_SEQUENCE_MONOCHROME
    elif style_preset == "vibrant":
        color_seq = COLOR_SEQUENCE_PRIMARY
    elif style_preset == "dark":
        fig.update_layout(
            paper_bgcolor=COLORS["bg_dark"],
            plot_bgcolor="#0a3d0c",
            font_color=COLORS["text_inverse"]
        )
        color_seq = COLOR_SEQUENCE_PRIMARY
    else:
        color_seq = color_sequence or COLOR_SEQUENCE_PRIMARY
    
    # Update trace colors
    for i, trace in enumerate(fig.data):
        color_idx = i % len(color_seq)
        
        if hasattr(trace, 'marker'):
            if isinstance(trace, (go.Bar, go.Scatter, go.Scattergl)):
                trace.marker.color = color_seq[color_idx]
            elif isinstance(trace, go.Pie):
                trace.marker.colors = color_seq
                
        if hasattr(trace, 'line') and isinstance(trace, go.Scatter):
            if trace.mode and 'lines' in trace.mode:
                trace.line.color = color_seq[color_idx]
    
    return fig

def add_annotations_styled(fig: go.Figure, 
                          annotations: List[Dict[str, Any]]) -> go.Figure:
    """
    Add styled annotations to a figure.
    """
    for ann in annotations:
        ann.update({
            'font': dict(
                family=FONT_FAMILY,
                size=ann.get('font', {}).get('size', FONT_SIZE_SMALL),
                color=ann.get('font', {}).get('color', COLORS["text_primary"])
            ),
            'bgcolor': ann.get('bgcolor', 'rgba(255, 255, 255, 0.9)'),
            'bordercolor': ann.get('bordercolor', COLORS["border"]),
            'borderwidth': ann.get('borderwidth', 1),
            'borderpad': ann.get('borderpad', 4),
        })
    
    fig.update_layout(annotations=annotations)
    return fig

def create_subplot_figure(rows: int, cols: int, 
                         subplot_titles: Optional[List[str]] = None,
                         **kwargs) -> go.Figure:
    """
    Create a subplot figure with Energisme styling pre-applied.
    """
    from plotly.subplots import make_subplots
    
    fig = make_subplots(
        rows=rows, 
        cols=cols,
        subplot_titles=subplot_titles,
        horizontal_spacing=0.1,
        vertical_spacing=0.15,
        **kwargs
    )
    
    # Apply template
    fig.update_layout(template="energisme+ggplot2")
    
    # Update subplot title styling
    for annotation in fig.layout.annotations:
        annotation.update(
            font=dict(
                size=FONT_SIZE_SUBTITLE,
                color=COLORS["text_primary"],
                family=FONT_FAMILY
            )
        )
    
    return fig

