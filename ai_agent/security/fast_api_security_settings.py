"""FastApiSettingsMixin."""

import os
from typing import Any, TypedDict

import fastapi
import jwt
import requests
from fastapi import Depends, HTTPException
from fastapi.security import OAuth2PasswordBearer
from jwcrypto.jwk import JW<PERSON>

from ai_agent.utils.utils import get_logger

logger = get_logger(__name__)


class TokenInfo(TypedDict):
    """Represents token information after decoding."""

    email: str
    partner: str
    scopes: set[str]
    token: str


def get_public_key_web_content(public_key_url: str | None) -> list[dict[str, Any]]:
    """Get the public key from a given URL."""
    if public_key_url:
        response = requests.get(url=public_key_url, timeout=60)
        if response.status_code == fastapi.status.HTTP_200_OK:
            return response.json().get("keys")

        logger.warning("Error when get public key, status: %s, response: %s", response.status_code, response.text)
    return []


class FastApiSecuritySettings:
    """FastApi security settings."""

    oauth2_scheme: OAuth2PasswordBearer = OAuth2PasswordBearer(tokenUrl="token")
    public_key_url: str | None = os.environ["PUBLIC_KEY_URL"] if os.getenv("PUBLIC_KEY_URL") else None
    public_key_web_content: list[dict[str, Any]] = get_public_key_web_content(public_key_url)

    @classmethod
    def get_public_key(cls, index: int) -> bytes:
        """Get public key from the stored public key content.

        This method fetches a public key from `public_key_web_content` using the provided index
        and exports it in PEM format.

        Args:
            index (int): The index of the key in the list of public keys.

        Returns:
            bytes: The public key in PEM format.

        Raises:
            HTTPException: If the key cannot be retrieved or is invalid.

        """
        try:
            header_key = cls.public_key_web_content[index]
            return JWK(**header_key).export_to_pem()
        except Exception as exception:
            raise HTTPException(detail="INVALID_KEY", status_code=fastapi.status.HTTP_401_UNAUTHORIZED) from exception

    @classmethod
    def get_token_info(
        cls,
        token: str = Depends(oauth2_scheme),
        audience: str = "account",
        index: int = 0,
    ) -> TokenInfo:
        """Decode and validate a JWT token.

        This method decodes a JWT token using the public key retrieved from `get_public_key`.
        It verifies the audience and ensures the token is valid.

        Args:
            token (str): The JWT token to decode.
            audience (str): The expected audience for the token. Default is "account".
            index (int): The index of the public key in the key set.

        Returns:
            TokenInfo: An object containing user information from the decoded token.

        Raises:
            HTTPException: If the token cannot be decoded or is invalid.

        """
        try:
            decoded_token = jwt.decode(token, cls.get_public_key(index), audience=audience, algorithms=["RS256"])
        except jwt.ExpiredSignatureError as error:
            raise HTTPException(status_code=fastapi.status.HTTP_401_UNAUTHORIZED, detail="EXPIRED_TOKEN") from error
        except jwt.InvalidIssuerError as error:
            raise HTTPException(status_code=fastapi.status.HTTP_401_UNAUTHORIZED, detail="INVALID_ISSUER") from error
        except jwt.InvalidTokenError as error:
            raise HTTPException(status_code=fastapi.status.HTTP_401_UNAUTHORIZED, detail="INVALID_TOKEN") from error

        if decoded_token.get("partner") is None and decoded_token.get("scopes"):
            raise HTTPException(status_code=fastapi.status.HTTP_403_FORBIDDEN, detail="FORBIDDEN")

        return TokenInfo(
            email=decoded_token.get("email"),
            scopes=set(decoded_token.get("scope").split(" ") if decoded_token["scope"] else []),
            partner=decoded_token.get("partner"),
            token=token,
        )

    @classmethod
    def has_scopes(cls, required_scopes: set[str], token: str = Depends(oauth2_scheme)) -> TokenInfo:
        """Check if a user has the required scopes.

        This method ensures that the authenticated user has the necessary scopes
        to access a resource.

        Args:
            required_scopes (Set[str]): A set of scopes required to access the resource.
            token (str): The JWT token to decode.

        Returns:
            TokenInfo: An object containing user information from the decoded token.

        Raises:
            HTTPException: If the token cannot be decoded or is invalid.

        """
        token_info = cls.get_token_info(token)
        scopes = token_info["scopes"]
        if not required_scopes.issubset(scopes):
            raise HTTPException(status_code=fastapi.status.HTTP_403_FORBIDDEN, detail="FORBIDDEN")
        return token_info
