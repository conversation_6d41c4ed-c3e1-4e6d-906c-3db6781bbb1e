"""
Conversation-summarisation helper
================================

Supports **two independent criteria** for deciding when to summarise:

1.  *Message count*   – preserve N messages, trigger after M new ones (old behaviour)
2.  *Token count*     – preserve ≈N tokens, trigger after ≈M tokens

Switch by setting `policy.mode` to `"messages"` or `"tokens"`.
The token-based path uses **langchain_core.messages.utils.count_tokens_approximately**
(no tiktoken dependency required).
"""

from __future__ import annotations

from dataclasses import dataclass
from typing import Iterable, List, Dict, Any, Literal, Optional
from langchain_core.prompts import ChatPromptTemplate

from langchain_core.messages import SystemMessage, HumanMessage, ToolMessage, RemoveMessage,BaseMessage,AIMessage
from langchain_core.messages.utils import count_tokens_approximately
from langchain_openai import ChatOpenAI
from langchain_deepseek import ChatDeepSeek
from langchain_google_genai import ChatGoogleGenerativeAI
from langchain_anthropic import ChatAnthropic

from ai_agent.model import summary_model,prompt as system_prompt
import uuid
from pathlib import Path
import os
import json
import re

import logging

# Set up logging 
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

#-----------------------------------------------------------------------------#
#  Data summary
#-----------------------------------------------------------------------------#

def deduplicate_input_data(input_data):
    """Remove duplicates from input_data."""
    seen = set()
    unique = []
    for item in input_data:
        frozen = tuple(sorted(item.items())) if isinstance(item, dict) else item
        if frozen not in seen:
            seen.add(frozen)
            unique.append(item)
    return unique

def clean_state_data(state):
    """Remove invalid input_data paths and append cleanup logs."""
    print("DEBUG - Cleaning state data")
    input_data = state.get("input_data", [])
    print(f"DEBUG - Input data before cleaning: {input_data}")

    state["input_data"] = deduplicate_input_data(input_data)
    print(f"DEBUG - Input data after deduplication: {state['input_data']}")

    valid_data = []
    for item in state["input_data"]:
        # Check if the item has a sandbox_file_name
        sandbox_file_name = item.get("sandbox_file_name") if isinstance(item, dict) else getattr(item, "sandbox_file_name", "")
        if sandbox_file_name:
            print(f"DEBUG - Item has sandbox_file_name: {sandbox_file_name}")
            # Don't check if the sandbox file exists locally, just keep it
            valid_data.append(item)
            continue

        # If no sandbox_file_name, check if data_path exists locally
        path = item.get("data_path") if isinstance(item, dict) else getattr(item, "data_path", "")
        if path and os.path.exists(path):
            print(f"DEBUG - Local file exists: {path}")
            valid_data.append(item)
        else:
            msg = f"Removed invalid data reference: {path}"
            print(f"[CLEANUP] {msg}")
            state.setdefault("requests_error", []).append(msg)

    state["input_data"] = valid_data
    print(f"DEBUG - Input data after cleaning: {state['input_data']}")
    return state

async def _get_dataframe_info_from_file_in_sandbox(client, session_id, file_path):
    """Get detailed information about a DataFrame by reading the file directly in the sandbox.

    This is a simplified version that only gets the essential information needed for the data summary.
    """
    print(f"DEBUG - Getting DataFrame info for file: {file_path} in session: {session_id}")

    # Code to execute in the sandbox to get DataFrame information
    code = f"""
import pandas as pd
import json

try:
    # Read the CSV file
    file_path = "{file_path}"
    df = pd.read_csv(file_path)

    # Get basic info
    info = {{
        "type": "DataFrame",
        "shape": df.shape,
        "columns": df.columns.tolist(),
        "dtypes": {{col: str(dtype) for col, dtype in df.dtypes.items()}},
        "description": f"DataFrame: {{df.shape[0]}}×{{df.shape[1]}}"
    }}

    print("JSON_DATA_START")
    print(json.dumps(info))
    print("JSON_DATA_END")
except Exception as e:
    print("JSON_DATA_START")
    print(json.dumps({{"error": str(e)}}))
    print("JSON_DATA_END")
"""

    try:
        # Execute the code in the sandbox
        result = await client.execute_code(session_id, code)
        output = result.get("output", "")

        # Extract the JSON data from the output
        import re
        json_match = re.search(r'JSON_DATA_START\s*(.*?)\s*JSON_DATA_END', output, re.DOTALL)
        if json_match:
            json_str = json_match.group(1).strip()
            return json.loads(json_str)

        return {"error": "Could not parse DataFrame info"}
    except Exception as e:
        print(f"DEBUG - Error getting DataFrame info: {str(e)}")
        return {"error": f"Error querying sandbox: {str(e)}"}

def _format_dataframe_info(df_info):
    """Format DataFrame information into a markdown string.

    Args:
        df_info: Dictionary containing DataFrame information from the sandbox
                 with keys: shape, columns, dtypes

    Returns:
        Formatted markdown string with DataFrame information
    """
    summary = ""

    # Add shape info
    if "shape" in df_info:
        rows, cols = df_info["shape"]
        summary += f"- **Shape**: {rows} rows × {cols} columns\n"

    # Add data types
    if "dtypes" in df_info:
        summary += "- **Data Types**:\n"
        for col, dtype in df_info["dtypes"].items():
            summary += f"  - {col}: {dtype}\n"

    return summary

def create_data_summary(state) -> str:
    """Creates a markdown-formatted summary of the current app state using enhanced DataFrame information."""
    # Debug: Print the state to see what's available
    print("DEBUG - State in create_data_summary:")
    print(f"  - input_data: {state.get('input_data', [])}")
    print(f"  - current_variables: {state.get('current_variables', {})}")

    # Print the type of input_data to help diagnose issues
    input_data = state.get('input_data', [])
    print(f"  - input_data type: {type(input_data)}")
    if input_data:
        print(f"  - first item type: {type(input_data[0])}")
        print(f"  - first item attributes: {dir(input_data[0]) if hasattr(input_data[0], '__dict__') else input_data[0]}")

    # Convert input_data to a standard format
    standardized_input_data = []
    for item in input_data:
        if isinstance(item, dict):
            # Already a dictionary, just use it
            standardized_input_data.append(item)
        elif hasattr(item, '__dict__'):
            # Convert object to dictionary
            item_dict = {}
            for attr in ['variable_name', 'data_path', 'data_description', 'sandbox_file_name']:
                if hasattr(item, attr):
                    item_dict[attr] = getattr(item, attr)
            standardized_input_data.append(item_dict)
        else:
            # Unknown type, just add it as is
            standardized_input_data.append(item)

    # Replace the original input_data with the standardized version
    state['input_data'] = standardized_input_data
    print(f"  - standardized_input_data: {standardized_input_data}")

    # Check if session_id is in state
    session_id = state.get("session_id")
    if not session_id:
        print("WARNING: No session_id found in state, cannot query sandbox for DataFrame info")

    state = clean_state_data(state)
    summary = ""
    known_vars = set()

    # 🧾 Section: Available Datasets (matching sync version structure)
    input_data = state.get("input_data", [])
    if input_data:
        summary += "## Available Datasets:\n"
        for idx, item in enumerate(input_data):
            # All items should now be dictionaries
            var = item.get("variable_name", f"dataset_{idx}")
            path = item.get("data_path", "unknown")
            desc = item.get("data_description", "No description")
            sandbox_path = item.get("sandbox_file_name", "unknown")

            summary += f"\n### {var}\n- **Description**: {desc}\n- **Path**: {sandbox_path if sandbox_path != 'unknown' else path}\n"

            # Debug print
            print(f"DEBUG - Dataset {idx}: var={var}, path={path}, sandbox_file={sandbox_path}")

            # Check if this variable exists in current_variables with enhanced DataFrame info
            current_vars = state.get("current_variables", {})
            if var in current_vars and isinstance(current_vars[var], dict) and current_vars[var].get("type") == "DataFrame":
                # Use the enhanced DataFrame info directly from current_variables
                df_info = current_vars[var]
                summary += _format_dataframe_info(df_info)
            # If not in current_variables but has a sandbox_file_name, get info directly from the sandbox
            elif sandbox_path and sandbox_path != "unknown":
                print("DEBUG: USE SANDBOX TO GET VARIABLES INFOS")
                # Get session_id from state
                session_id = state.get("session_id")
                if session_id:
                    try:
                        import asyncio
                        from ai_agent.sandbox_client import SandboxClient
                        client = SandboxClient()
                        # Get DataFrame info directly from the sandbox
                        df_info = asyncio.run(_get_dataframe_info_from_file_in_sandbox(client, session_id, sandbox_path))
                        if "error" not in df_info:
                            summary += _format_dataframe_info(df_info)
                        else:
                            summary += f"- **Error**: {df_info['error']}\n"
                    except Exception as e:
                        print(f"DEBUG - Error getting DataFrame info from sandbox: {str(e)}")
                        summary += f"- **Error**: Could not get DataFrame information: {str(e)}\n"
                else:
                    summary += "- **Note**: Session ID not available, cannot retrieve DataFrame information\n"

            known_vars.add(var)


    return summary


#-----------------------------------------------------------------------------#
#  Control message Id's and Duplication
#-----------------------------------------------------------------------------#

def ensure_message_ids(messages):
    """
    Ensure all messages have IDs by assigning UUIDs to those without IDs.

    Args:
        messages: List of messages to process

    Returns:
        List of messages with IDs assigned
    """
    for i, msg in enumerate(messages):
        # Skip RemoveMessage objects
        if isinstance(msg, RemoveMessage):
            continue

        # If message doesn't have an ID, assign one
        if not hasattr(msg, "id") or not msg.id:
            # Generate a UUID
            msg_id = str(uuid.uuid4())
            # Assign the ID to the message
            setattr(msg, "id", msg_id)
            print(f"[Debug] Assigned ID {msg_id} to message {i+1}")

    return messages

def remove_duplicate_messages(messages):
    """
    Remove duplicate messages from the message list using message IDs.

    Args:
        messages: List of messages to deduplicate

    Returns:
        Tuple of (deduplicated_messages, remove_messages)
    """
    # Track seen message IDs to identify duplicates
    seen_ids = set()
    seen_content_by_type = {}
    duplicates = []
    unique_messages = []

    for msg in messages:
        # Skip RemoveMessage objects
        if isinstance(msg, RemoveMessage):
            unique_messages.append(msg)
            continue

        # First check by ID if available
        if hasattr(msg, "id") and msg.id:
            if msg.id in seen_ids:
                # This is a duplicate by ID, add to duplicates list
                duplicates.append(msg)
                continue
            else:
                # This is a new message by ID
                seen_ids.add(msg.id)
                unique_messages.append(msg)
                continue

        # If no ID, check by content and type
        msg_type = type(msg).__name__
        msg_content = getattr(msg, "content", "")

        # Create a key for this message type and content
        content_key = f"{msg_type}:{msg_content}"

        if content_key in seen_content_by_type:
            # This is a duplicate by content, add to duplicates list
            duplicates.append(msg)
        else:
            # This is a new message by content
            seen_content_by_type[content_key] = True
            unique_messages.append(msg)

    # Create RemoveMessage objects for duplicates
    remove_messages = []
    for msg in duplicates:
        if hasattr(msg, "id") and msg.id:
            remove_messages.append(RemoveMessage(id=msg.id))

    return unique_messages, remove_messages
# ─────────────────────────────────────────────────────────────────────────────
#  Configuration
# ─────────────────────────────────────────────────────────────────────────────

@dataclass
class SummarizationPolicy:
    # "messages" keeps the old behaviour, "tokens" uses approx-token counting.
    mode: Literal["messages", "tokens"] = "messages"

    # message-based thresholds
    messages_to_preserve: int = 2
    trigger_threshold_msgs: int = 5

    # token-based thresholds
    tokens_to_preserve: int = 1000
    trigger_threshold_toks: int = 2000

    # when True, prints rich debug information
    verbose: bool = False


# ─────────────────────────────────────────────────────────────────────────────
#  Public API
# ─────────────────────────────────────────────────────────────────────────────

def summarize_conversation_agent(
    non_system_messages: List[BaseMessage],
    *,
    policy: SummarizationPolicy,
    previous_summary: Optional[str] = None,
    id_last_summary: Optional[str] = None,
) -> Dict[str, Any]:
    """
    Summarise conditionally.  Delegates to the appropriate branch.
    """
    if policy.mode == "messages":
        return _summarise_by_message_count(
            non_system_messages, policy, previous_summary, id_last_summary
        )
    return _summarise_by_token_count(
        non_system_messages, policy, previous_summary, id_last_summary
    )


def pre_model_hook(state):
    """
    Hook executed before each LLM call.
    Returns the trimmed + summarised message list together with
    updated summary metadata.
    """
    policy = SummarizationPolicy(mode="messages", verbose=True)
    # 1 Deduplicate + strip system messages
    messages = ensure_message_ids(state["messages"])
    unique_messages, _ = remove_duplicate_messages(messages)
    non_system = [m for m in unique_messages if not isinstance(m, SystemMessage)]

    prev_summary   = state.get("summary")
    prev_last_id   = state.get("id_last_summary")
    images = state.get("output_image_paths", [])
    print("--------------------------------")
    print(f"[IMAGES]: {images}")
    print("--------------------------------")
    # 2 Run summariser (it decides internally whether to fire)
    result = summarize_conversation_agent(
        non_system,
        policy=policy,
        previous_summary=prev_summary,
        id_last_summary=prev_last_id,
    )
    print(f"[Variables in the state]: {state.get('current_variables', {})}")
    print(f"[Session ID in state]: {state.get('session_id')}")
    print(f"[Input data in state]: {state.get('input_data', [])}")

    # Check if there's any tool output in the messages that contains input_data
    tool_input_data = []
    for msg in state.get("messages", []):
        if hasattr(msg, "content") and isinstance(msg.content, str):
            if "input_data" in msg.content:
                try:
                    # Try to extract input_data from the message content
                    content_dict = json.loads(msg.content)
                    if "input_data" in content_dict and content_dict["input_data"]:
                        print(f"Found input_data in message: {content_dict['input_data']}")
                        tool_input_data.extend(content_dict["input_data"])
                except:
                    pass

    # If we found input_data in the messages, add it to the state
    if tool_input_data:
        print(f"Adding input_data from tool output: {tool_input_data}")
        state["input_data"] = tool_input_data

    # 3 Compose new system prompt
    # Make sure session_id is in the state for sandbox access
    if not state.get("session_id"):
        print("WARNING: No session_id found in state, generating a random one")
        state["session_id"] = str(uuid.uuid4())

    # Call create_data_summary with the state containing session_id and enhanced DataFrame info
    data_ctx = create_data_summary(state)

    print("²²²²²²²²²²²²²²²²²²²²²²²²Data description²²²²²²²²²²²²²²²²²²²²²²²²")
    print(data_ctx)
    print("²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²²")
    sys_parts = [
        system_prompt,
        "\n\n# Current Data Context\n",
        data_ctx if data_ctx else "No data available yet.",
    ]
    if result["summary"]:
        sys_parts.extend(
            ["\n\n# Recent Conversation Summary\n", result["summary"]]
        )

    # Always print the system prompt for debugging
    print("+++++++++++System prompt++++++++++++++")
    print(sys_parts)
    print("++++++++++++++++++++++++++++++++++++++")

    sys_msg = SystemMessage(content="".join(sys_parts))
    final_msgs = [sys_msg] + result["preserved_messages"]

    _debug(
        policy,
        "pre_model_hook",
        preserved=len(result["preserved_messages"]),
        mode=policy.mode,
        summary_added=bool(result["summary"]),
    )

    # Debug: Print the result messages (trimmed version)
    print(f"[Message boundary ID] {result['last_preserved_id']}")
    print("\n[Debug] Messages passed to the LLM (trimmed):")
    for i, msg in enumerate(final_msgs):
        msg_type = type(msg).__name__
        if hasattr(msg, "content"):
            content = msg.content[:50] + "..." if len(msg.content) > 50 else msg.content
            print(f"  {i}. {msg_type}: {content}")
        else:
            print(f"  {i}. {msg_type}: [No content attribute]")
    return {
        "llm_input_messages": final_msgs,
        "summary": result["summary"],
        "id_last_summary": result["last_preserved_id"],
    }

# ─────────────────────────────────────────────────────────────────────────────
#  Boundary fix: never start a preserved block with a lone ToolMessage
# ─────────────────────────────────────────────────────────────────────────────
def _adjust_if_tool_first(msgs: List[BaseMessage], idx: int) -> int:
    """If msgs[idx] is a ToolMessage, back up one position (when possible)."""
    if idx > 0 and isinstance(msgs[idx], ToolMessage) and not isinstance(msgs[idx-1], ToolMessage):
        return idx - 1
    return idx
# ─────────────────────────────────────────────────────────────────────────────
#  Internal helpers – message-based
# ─────────────────────────────────────────────────────────────────────────────

def _summarise_by_message_count(
    msgs: List[BaseMessage],
    p: SummarizationPolicy,
    prev: Optional[str],
    last_id: Optional[str],
):
    # Boundary for the trailing "tail" to keep
    raw_start = max(0, len(msgs) - p.messages_to_preserve)
    start = (
        max(0, raw_start - 1)
        if raw_start < len(msgs) and isinstance(msgs[raw_start], ToolMessage)
        else raw_start
    )

    # Index right after the last summary
    after_last = 0
    if last_id:
        for i, m in enumerate(msgs):
            if m.id == last_id:
                after_last = i + 1
                break

    msgs_to_sum = msgs[after_last:start]

    _debug(p, "msg-policy boundary",
           new_messages=len(msgs_to_sum), trigger=p.trigger_threshold_msgs)

    # ── Skip path ──────────────────────────────────────────────────────────
    if len(msgs_to_sum) < p.trigger_threshold_msgs:
        preserved_from = after_last
        preserved_from = _adjust_if_tool_first(msgs, after_last)                  # unsummarised + tail
        preserved = msgs[preserved_from:]
        return {
            "preserved_messages": preserved,
            "summary": prev,
            "last_preserved_id": last_id,
        }

    print("!!!!!!!!!!!!Summarized message!!!!!!!!!!!!!!!!!!!!!")
    for i, msg in enumerate(msgs_to_sum):
        msg_type = type(msg).__name__
        if hasattr(msg, "content"):
            content = msg.content[:50] + "..." if len(msg.content) > 50 else msg.content
            print(f"  {i}. {msg_type}: {content}")
        else:
            print(f"  {i}. {msg_type}: [No content attribute]")
    print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!")
    # ── Summarise path ────────────────────────────────────────────────────
    summary   = _invoke_summary_model(msgs_to_sum, prev)
    print("================Summary=================")
    print(summary)
    print("========================================")
    preserved = msgs[start:]                        # tail only
    return {
        "preserved_messages": preserved,
        "summary": summary,
        "last_preserved_id": preserved[0].id if preserved else last_id,
    }


# ─────────────────────────────────────────────────────────────────────────────
#  Internal helper – token-based policy
# ─────────────────────────────────────────────────────────────────────────────
def _summarise_by_token_count(
    msgs: List[BaseMessage],
    p: SummarizationPolicy,
    prev: Optional[str],
    last_id: Optional[str],
):
    toks = [count_tokens_approximately([m]) for m in msgs]

    # Walk backwards for ≈ tokens_to_preserve
    running = 0
    start   = len(msgs)
    for i in range(len(msgs) - 1, -1, -1):
        running += toks[i]
        if running >= p.tokens_to_preserve:
            start = i
            break
    if start < len(msgs) and isinstance(msgs[start], ToolMessage):
        start = max(0, start - 1)

    after_last = 0
    if last_id:
        for i, m in enumerate(msgs):
            if m.id == last_id:
                after_last = i + 1
                break

    slice_msgs   = msgs[after_last:start]
    slice_tokens = sum(toks[i] for i in range(after_last, start))

    _debug(p, "token-policy boundary",
           new_tokens=slice_tokens, trigger=p.trigger_threshold_toks)

    # ── Skip path ──────────────────────────────────────────────────────────
    if slice_tokens < p.trigger_threshold_toks:
        preserved_from = after_last                  # unsummarised + tail
        preserved = msgs[preserved_from:]
        return {
            "preserved_messages": preserved,
            "summary": prev,
            "last_preserved_id": last_id,
        }

    # ── Summarise path ────────────────────────────────────────────────────
    summary   = _invoke_summary_model(slice_msgs, prev)
    preserved = msgs[start:]
    preserved_from = _adjust_if_tool_first(msgs, after_last)                        # tail only
    return {
        "preserved_messages": preserved,
        "summary": summary,
        "last_preserved_id": preserved[0].id if preserved else last_id,
    }

# ─────────────────────────────────────────────────────────────────────────────
#  Utilities
# ─────────────────────────────────────────────────────────────────────────────



# ─────────────────────────────────────────────────────────────────────────────
#  LLM call wrapper – stricter prompt        (FINAL PATCH)
# ─────────────────────────────────────────────────────────────────────────────
def _invoke_summary_model(
    msgs_to_summarise: List[BaseMessage],
    prev_summary: str | None,
) -> str:
    # 1) Build a single block of text containing just the messages:
    convo_lines = []
    for m in msgs_to_summarise:
        if isinstance(m, HumanMessage):
            convo_lines.append(f"User: {m.content}")
        elif isinstance(m, AIMessage):
            convo_lines.append(f"Assistant: {m.content}")
        else:  # ToolMessage, etc.
            convo_lines.append(f"Tool Output: {m.content}")
    convo_text = "\n".join(convo_lines)
    # FIXED: Use logging instead of print to avoid UI contamination
    logger.debug("=" * 50)
    logger.debug(f"[convo_text]: {convo_text}")
    logger.debug("=" * 50)
    # 2) Construct one SystemMessage that wraps your instruction + convo_text
    if prev_summary:
        prompt_text = f"""
            You are an expert conversation summariser specializing in dialogues between users and data analysis copilots focused on consumption data (e.g., electricity, gas, water).
            Your goal is to create a concise summary that retains all critical information needed for the copilot to understand the context and continue the analysis in subsequent turns.

            Here is the initial dialogue:
            {convo_text}

            Write a concise bullet-point summary. Focus on capturing these key elements:
            * **User Intent/Goal:** What specific question(s) is the user trying to answer or what analysis are they requesting?
            * **Copilot Actions & Analysis:**
                * What data did the copilot retrieve or mention retrieving?
                * What specific analyses were performed (e.g., calculated average, plotted trend, identified anomaly)? Note any code execution if mentioned.
            * **Key Findings & Data Points:**
                * Were any specific values, results, or insights presented (e.g., "average daily usage is 15 kWh", "peak gas usage on Jan 10th")?
            * **Open Questions/Next Steps:** Are there outstanding questions from the user or agreed-upon next steps for the analysis?

            Keep the summary concise, but ensure these critical details are preserved for analytical continuity. Structure using bullet points.
            """

    else:
        prompt_text = f"""
            You are an expert conversation summariser specializing in dialogues between users and data analysis copilots focused on consumption data (e.g., electricity, gas, water).
            Your goal is to update the existing summary with information from new messages, creating a concise summary that retains all critical information needed for the copilot to understand the *current* context and continue the analysis.

            Below is the *previous* summary of the dialogue and then the *new* messages.

            Previous summary:
            {prev_summary}

            New messages:
            {convo_text}

            Update the summary in bullet points, integrating the information from the new messages. Focus *especially* on incorporating updates or additions related to:
            * **Evolving User Intent/Goal:** Has the user refined their question, asked a follow-up, or changed their focus?
            * **New Copilot Actions & Analysis:** What *new* data retrieval, analysis steps (including code execution), or calculations were performed in the latest messages?
            * **New Key Findings & Data Points:** What *new* results, values, or insights were presented? Include any user feedback or confirmation on previous findings.
            * **Updated Open Questions/Next Steps:** What are the current outstanding questions or agreed-upon next steps based on the latest interaction?

            Ensure the updated summary reflects the *current state* of the analysis. Base the update *only* on the provided previous summary and the new messages. Keep it concise, but prioritize retaining all critical details for analytical continuity. Structure using bullet points.
            """

    # 3) Call the model with just this one SystemMessage
    response = summary_model.invoke(prompt_text)
    return response.content.strip()



def _debug(policy: SummarizationPolicy, tag: str, **kv):
    """Conditional debug printer governed by policy.verbose."""
    if not policy.verbose:
        return
    parts = [f"[DEBUG:{tag}]"]
    parts += [f"{k}={v}" for k, v in kv.items()]
    print(" ".join(parts))

