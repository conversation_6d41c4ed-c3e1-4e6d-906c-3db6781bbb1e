include:
  - project: 'devops/ci-pipelines/devops-gitlab-pipeline'
    ref: '4.11.1'
    file:
      - '/gitlab-ci/templates/release-management/.pipeline-service.yml'

variables:
  APP_NAME: 'ai-agent'
  PACKAGE_NAME: 'ai_agent'

  PYTHON_VERSION: 3.13
  VENV_PATH: .venv
  PRODUCT_NAME: algo/ai

  HELM_VALUES_REPO: git.devops.energisme.com/algo/datascience/release-management/ai-services-values
  APPLICATION_RELEASE_REPO: algo/datascience/release-management/ai-services-application-release
  TRIVY_ALLOW_FAILURE: true

  PYTHON_UV_SLIM_IMAGE: ghcr.io/astral-sh/uv:python3.13-bookworm-slim
  DEPENDENCY_CHECK_IMAGE: acrdevopsvdc1.azurecr.io/dataarchi/devops/docker/dependency-check:1.0.1

  # The --mount option requires BuildKit. Refer to https://docs.docker.com/go/buildkit/ to learn how to build images with BuildKit enabled
  DOCKER_BUILDKIT: 1

# Cache downloaded dependencies and plugins between builds.
# To keep cache across branches add 'key: "$CI_JOB_NAME"'
cache:
  key: ${CI_COMMIT_REF_SLUG}
  paths:
    - ${VENV_PATH}/
    - .cache/uv
    - .dependency-check

.configure_uv_env: &configure_uv_env
  - |
      apt-get update && apt-get install -y --no-install-recommends \
        build-essential \
        g++ \
        gcc \
        libffi-dev \
        libssl-dev \
        python3-dev \
        && rm -rf /var/lib/apt/lists/* \
        && apt-get clean
  - uv venv $VENV_PATH
  - source $VENV_PATH/bin/activate
  - uv lock
  - uv sync

# Job - Execute unit-test
unit-test:
  image: "$PYTHON_UV_SLIM_IMAGE"
  stage: test
  before_script:
    - *configure_uv_env
  script:
    - uv run pytest --maxfail=5 --disable-warnings --cov=${PACKAGE_NAME} --cov-report=xml:coverage.xml
  tags:
    - build
  artifacts:
    paths:
      - coverage.xml
    expire_in: 1 day
  rules:
    - if: $CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_COMMIT_MESSAGE !~ /^Release/
    - if: $CI_COMMIT_TAG

dependency-check:
  image: "$DEPENDENCY_CHECK_IMAGE"
  stage: quality
  before_script:
    - *configure_uv_env
  script:
    - dependency-check.sh --scan "${VENV_PATH}/lib/python${PYTHON_VERSION}/site-packages" --format ALL --project "${APP_NAME}" --enableExperimental --nvdApiKey "${NVD_API_KEY}"
  tags:
    - build
  artifacts:
    when: always
    paths:
      - "./dependency-check-report.html"
      - "./dependency-check-report.json"
  rules:
    - if: $CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_COMMIT_MESSAGE !~ /^Release/
    - if: $CI_COMMIT_TAG

sonarqube_check:
  image: "$SONARQUBE_IMAGE"
  stage: quality
  script:
    - sonar-scanner -X -Dsonar.host.url=$SONAR_HOST_URL -Dsonar.token=$SONAR_TOKEN -Dproject.settings=sonar-project.properties
  tags:
    - build
  needs:
    - unit-test
    - dependency-check
  rules:
    - if: $CI_COMMIT_BRANCH && $CI_COMMIT_BRANCH != $CI_DEFAULT_BRANCH && $CI_COMMIT_MESSAGE !~ /^Release/
    - if: $CI_COMMIT_TAG
