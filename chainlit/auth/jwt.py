import os
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, Optional, <PERSON><PERSON>, TypedDict, Set

import jwt as pyjwt
from jwt import PyJWKClient
import httpx
import fastapi
from fastapi import Depends, HTTPException
from fastapi.security import OA<PERSON>2<PERSON><PERSON><PERSON><PERSON>earer

from chainlit.config import config
from chainlit.user import User
from chainlit.logger import logger


def get_jwt_secret() -> Optional[str]:
    return os.environ.get("CHAINLIT_AUTH_SECRET")


def create_jwt(data: User) -> str:
    to_encode: Dict[str, Any] = data.to_dict()
    to_encode.update(
        {
            "exp": datetime.now(timezone.utc)
            + timedelta(seconds=config.project.user_session_timeout),
            "iat": datetime.now(timezone.utc),  # Add issued at time
        }
    )

    secret = get_jwt_secret()
    assert secret
    encoded_jwt = pyjwt.encode(to_encode, secret, algorithm="HS256")
    return encoded_jwt


# ---------------------------------------------------------------------------
# Support for external OIDC (e.g. Keycloak) tokens signed with RS256.
# ---------------------------------------------------------------------------
# If one of the following environment variables is set, `decode_jwt` will
# validate the token against the provider's JWKS instead of using the shared
# HS256 secret:
#   • CHAINLIT_JWKS_URL              – full URL to the JWKS endpoint.
#   • CHAINLIT_OPENID_CONFIG_URL     – full URL to the OIDC discovery doc
#                                       (ends with /.well-known/openid-configuration).
#
# Optional additional env variables:
#   • CHAINLIT_JWT_AUDIENCE          – expected audience claim ("aud").
#   • CHAINLIT_JWT_ISSUER            – expected issuer claim ("iss"). If not
#                                       provided and OPENID_CONFIG is used we
#                                       derive it from the discovery doc.
# ---------------------------------------------------------------------------

# Simple in-process cache for the fetched discovery document and JWKS URL so we
# don't re-fetch them on every token validation.
_oidc_cache: Dict[str, Tuple[Dict[str, Any], float]] = {}


def _get_from_cache(key: str) -> Optional[Dict[str, Any]]:
    entry = _oidc_cache.get(key)
    if not entry:
        return None
    doc, expires_at = entry
    if expires_at < datetime.now(timezone.utc).timestamp():
        _oidc_cache.pop(key, None)
        return None
    return doc


def _set_cache(key: str, value: Dict[str, Any], ttl: int = 3600):
    _oidc_cache[key] = (value, datetime.now(timezone.utc).timestamp() + ttl)


def _fetch_openid_configuration(url: str) -> Dict[str, Any]:
    """Fetch and cache the provider's openid-configuration document."""

    if doc := _get_from_cache(url):
        return doc

    resp = httpx.get(url, timeout=5)
    resp.raise_for_status()
    doc = resp.json()

    _set_cache(url, doc)
    return doc


def _get_jwks_client_and_metadata() -> Optional[Tuple[PyJWKClient, Dict[str, Any]]]:
    """Return a JWKS client (PyJWKClient) and the discovery metadata if configured."""

    jwks_url = os.environ.get("CHAINLIT_JWKS_URL")
    discovery_url = os.environ.get("CHAINLIT_OPENID_CONFIG_URL")

    # If nothing configured via env, return None to fall back to HS256
    if not jwks_url and not discovery_url:
        return None

    metadata: Dict[str, Any] = {}

    if not jwks_url and discovery_url:
        # Resolve JWKS URL through the discovery document
        doc = _fetch_openid_configuration(discovery_url)
        jwks_url = doc.get("jwks_uri")
        if not jwks_url:
            raise ValueError("jwks_uri not found in discovery document")
        metadata["issuer"] = doc.get("issuer")

    if not jwks_url:
        raise ValueError("CHAINLIT_JWKS_URL must be set when not using CHAINLIT_OPENID_CONFIG_URL")

    jwk_client = PyJWKClient(jwks_url)  # type: ignore[arg-type]
    return jwk_client, metadata


def decode_jwt(token: str) -> User:
    logger.debug("decode_jwt: starting decode; token length=%d", len(token))

    jwks_meta = _get_jwks_client_and_metadata()

    if jwks_meta is not None:
        jwk_client, metadata = jwks_meta
        logger.debug("decode_jwt: using external JWKS from %s", jwk_client.jwks_uri)  # type: ignore[attr-defined]

        try:
            signing_key = jwk_client.get_signing_key_from_jwt(token).key  # network on first call, cached afterwards
        except Exception as e:
            logger.error("decode_jwt: unable to locate signing key for token header kid=%s : %s", pyjwt.get_unverified_header(token).get('kid'), e)
            raise

        logger.debug("decode_jwt: obtained signing key kid=%s", getattr(signing_key, 'kid', 'n/a'))

        verify_args: Dict[str, Any] = {
            "algorithms": ["RS256"],
            "options": {"verify_signature": True},
        }

        # Audience & issuer verification are optional – only applied if provided.
        audience = os.environ.get("CHAINLIT_JWT_AUDIENCE")
        issuer = os.environ.get("CHAINLIT_JWT_ISSUER") or metadata.get("issuer")

        # Only validate audience if explicitly configured
        if audience and os.environ.get("CHAINLIT_JWT_AUDIENCE"):
            verify_args["audience"] = audience
        else:
            # Disable aud verification by default to be more flexible
            verify_args.setdefault("options", {}).update({"verify_aud": False})

        # Only validate issuer if explicitly configured
        if issuer and os.environ.get("CHAINLIT_JWT_ISSUER"):
            verify_args["issuer"] = issuer
        else:
            # Disable issuer verification by default to be more flexible
            verify_args.setdefault("options", {}).update({"verify_iss": False})

        logger.debug("decode_jwt: verifying RS256 token with args=%s", verify_args)
        try:
            data = pyjwt.decode(token, signing_key, **verify_args)  # type: ignore[arg-type]
        except Exception as e:
            logger.error("decode_jwt: RS256 verification failed: %s", e)
            raise

        logger.debug("decode_jwt: verification successful; claims=%s", {k: data.get(k) for k in ('sub','aud','iss','exp')})

    else:
        # Fallback to the original HS256 shared-secret verification (local dev).
        secret = get_jwt_secret()
        logger.debug("decode_jwt: falling back to HS256 shared secret")
        if not secret:
            # If we have JWKS configuration but no HS256 secret, that's fine
            # The error will be handled by the caller
            raise ValueError("CHAINLIT_AUTH_SECRET must be set when not using external JWT validation")

        try:
            data = pyjwt.decode(
                token,
                secret,
                algorithms=["HS256"],
                options={"verify_signature": True},
            )
        except Exception as e:
            logger.error("decode_jwt: HS256 verification failed: %s", e)
            raise

        logger.debug("decode_jwt: HS256 verification successful; claims=%s", {k: data.get(k) for k in ('sub','aud','iss','exp')})

    # Build a User object from common OIDC claims.
    identifier = (
        data.get("email")
        or data.get("preferred_username")
        or data.get("sub")
    )
    if not identifier:
        raise ValueError("No suitable identifier (email, preferred_username, sub) found in JWT claims")

    display_name = data.get("name") or data.get("given_name") or data.get("preferred_username")

    # Keep only safe metadata (roles etc.)
    metadata = {
        k: v
        for k, v in data.items()
        if k
        not in [
            "sub",
            "email",
            "preferred_username",
            "name",
            "given_name",
            "family_name",
            "aud",
            "iss",
            "iat",
            "nbf",
            "exp",
            "partner",  # Partner is session-specific, not user-specific
        ]
    }

    return User(identifier=identifier, display_name=display_name, metadata=metadata)


class TokenInfo(TypedDict):
    email: str
    partner: str | None
    scopes: Set[str]
    token: str


class FastApiSecuritySettings:
    """
    Security helper that validates RS256 JWTs against a remote JWKS, with an
    HS256 fallback for local development (when CHAINLIT_AUTH_SECRET is set).
    """

    oauth2_scheme: OAuth2PasswordBearer = OAuth2PasswordBearer(tokenUrl="token")

    # ──────────────────────────────────────────────────────────────────────────
    # RS256  🔒
    # ──────────────────────────────────────────────────────────────────────────
    @classmethod
    def _decode_rs256(cls, token: str) -> Dict[str, Any]:
        jwks_meta = _get_jwks_client_and_metadata()
        if not jwks_meta:
            raise ValueError("No JWKS configuration available")
            
        jwk_client, metadata = jwks_meta

        try:
            signing_key = jwk_client.get_signing_key_from_jwt(token).key
        except Exception as e:
            logger.error("FastApiSecuritySettings: unable to locate signing key for token header kid=%s : %s", 
                        pyjwt.get_unverified_header(token).get('kid'), e)
            raise

        verify_args: Dict[str, Any] = {
            "algorithms": ["RS256"],
            "options": {"verify_signature": True},
        }

        audience = os.environ.get("CHAINLIT_JWT_AUDIENCE")
        issuer = os.environ.get("CHAINLIT_JWT_ISSUER") or metadata.get("issuer")

        # Get token info for debugging
        unverified_header = pyjwt.get_unverified_header(token)
        unverified_payload = pyjwt.decode(token, options={"verify_signature": False})
        logger.debug("FastApiSecuritySettings: token header=%s", unverified_header)
        logger.debug("FastApiSecuritySettings: token payload issuer=%s, audience=%s", 
                    unverified_payload.get("iss"), unverified_payload.get("aud"))

        # Only validate audience if explicitly configured
        if audience and os.environ.get("CHAINLIT_JWT_AUDIENCE"):
            verify_args["audience"] = audience
        else:
            # Disable audience validation by default to be more flexible
            verify_args.setdefault("options", {})["verify_aud"] = False

        # Only validate issuer if explicitly configured
        if issuer and os.environ.get("CHAINLIT_JWT_ISSUER"):
            verify_args["issuer"] = issuer
        else:
            # Disable issuer validation by default to be more flexible
            verify_args.setdefault("options", {})["verify_iss"] = False

        logger.debug("FastApiSecuritySettings: verifying RS256 token with args=%s", verify_args)
        logger.debug("FastApiSecuritySettings: expected issuer=%s, expected audience=%s", issuer, audience)
        return pyjwt.decode(token, signing_key, **verify_args)  # type: ignore[arg-type]

    # ──────────────────────────────────────────────────────────────────────────
    # HS256  (local dev fallback)
    # ──────────────────────────────────────────────────────────────────────────
    @staticmethod
    def _decode_hs256(token: str) -> Dict[str, Any]:
        secret = get_jwt_secret()
        if not secret:
            # Don't raise HTTPException here, let the caller handle it
            raise ValueError("CHAINLIT_AUTH_SECRET missing for HS256 validation")
        return pyjwt.decode(token, secret, algorithms=["HS256"], options={"verify_signature": True})

    # ──────────────────────────────────────────────────────────────────────────
    # Public API
    # ──────────────────────────────────────────────────────────────────────────
    @classmethod
    def get_token_info(
        cls,
        token: str = Depends(oauth2_scheme),
        required_scopes: Set[str] | None = None,
    ) -> TokenInfo:
        """
        Validate *token* and return an enriched TokenInfo structure.
        Optionally ensures that *required_scopes* ⊆ token.scopes.
        """
        try:
            decoded = cls._decode_rs256(token)
            logger.debug("FastApiSecuritySettings: RS256 verification successful")
        except Exception as e:
            logger.debug("FastApiSecuritySettings: RS256 verification failed: %s", e)
            # RS256 failed → try HS256 (useful for unit tests / local runs)
            try:
                decoded = cls._decode_hs256(token)
                logger.debug("FastApiSecuritySettings: HS256 verification successful")
            except pyjwt.ExpiredSignatureError as exc:
                logger.error("FastApiSecuritySettings: token expired: %s", exc)
                raise HTTPException(
                    status_code=fastapi.status.HTTP_401_UNAUTHORIZED,
                    detail="EXPIRED_TOKEN",
                ) from exc
            except pyjwt.InvalidTokenError as exc:
                logger.error("FastApiSecuritySettings: invalid token: %s", exc)
                raise HTTPException(
                    status_code=fastapi.status.HTTP_401_UNAUTHORIZED,
                    detail="INVALID_TOKEN",
                ) from exc

        scopes: Set[str] = set(decoded.get("scope", "").split()) if decoded.get("scope") else set()
        partner = decoded.get("partner")

        if required_scopes and not required_scopes.issubset(scopes):
            raise HTTPException(
                status_code=fastapi.status.HTTP_403_FORBIDDEN, detail="FORBIDDEN"
            )

        # Example extra business-rule:
        if partner is None and scopes:
            raise HTTPException(
                status_code=fastapi.status.HTTP_403_FORBIDDEN, detail="FORBIDDEN"
            )

        email = decoded.get("email") or decoded.get("preferred_username") or decoded.get("sub")
        if not email:
            raise HTTPException(
                status_code=fastapi.status.HTTP_401_UNAUTHORIZED,
                detail="No suitable identifier (email, preferred_username, sub) found in JWT claims",
            )

        return TokenInfo(
            email=email,
            partner=partner,
            scopes=scopes,
            token=token,
        )

    # Convenience wrapper identical to the original `has_scopes`
    @classmethod
    def has_scopes(
        cls,
        required_scopes: Set[str],
        token: str = Depends(oauth2_scheme),
    ) -> TokenInfo:
        return cls.get_token_info(token=token, required_scopes=required_scopes)
