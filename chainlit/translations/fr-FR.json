{"common": {"actions": {"cancel": "Annuler", "confirm": "Confirmer", "continue": "<PERSON><PERSON><PERSON>", "goBack": "Retour", "reset": "Réinitialiser", "submit": "So<PERSON><PERSON><PERSON>"}, "status": {"loading": "Chargement...", "error": {"default": "Une erreur est survenue", "serverConnection": "Impossible de se connecter au serveur"}}}, "auth": {"login": {"title": "Connectez-vous pour accéder à l'application", "form": {"email": {"label": "Adresse e-mail", "required": "l'e-mail est requis"}, "password": {"label": "Mot de passe", "required": "le mot de passe est requis"}, "actions": {"signin": "Se connecter"}, "alternativeText": {"or": "OU"}}, "errors": {"default": "Impossible de se connecter", "signin": "Essayez de vous connecter avec un autre compte", "oauthSignin": "Essayez de vous connecter avec un autre compte", "redirectUriMismatch": "L'URI de redirection ne correspond pas à la configuration de l'application OAuth", "oauthCallback": "Essayez de vous connecter avec un autre compte", "oauthCreateAccount": "Essayez de vous connecter avec un autre compte", "emailCreateAccount": "Essayez de vous connecter avec un autre compte", "callback": "Essayez de vous connecter avec un autre compte", "oauthAccountNotLinked": "Pour confirmer votre identité, connectez-vous avec le même compte que celui utilisé initialement", "emailSignin": "Impossible d'envoyer l'e-mail", "emailVerify": "Veuillez vérifier votre e-mail, un nouveau message a été envoyé", "credentialsSignin": "Échec de la connexion. Vérifiez les informations fournies", "sessionRequired": "Veuillez vous connecter pour accéder à cette page"}}, "provider": {"continue": "Continuer avec {{provider}}"}}, "chat": {"input": {"placeholder": "Que recherchez-vous", "actions": {"send": "Envoyer le message", "stop": "<PERSON><PERSON><PERSON><PERSON> tâche", "attachFiles": "Jo<PERSON>re des fichiers"}}, "speech": {"start": "Démarrer l'enregistrement", "stop": "<PERSON><PERSON><PERSON><PERSON> l'enregistrement", "connecting": "Connexion en cours"}, "fileUpload": {"dragDrop": "Glissez-d<PERSON><PERSON>z les fichiers ici", "browse": "Parcourir les fichiers", "sizeLimit": "Limite :", "errors": {"failed": "Échec du téléversement", "cancelled": "Téléversement annulé de"}}, "messages": {"status": {"using": "Utilisation", "used": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"copy": {"button": "Copier dans le presse-papiers", "success": "Copié !"}}, "feedback": {"positive": "Utile", "negative": "Pas utile", "edit": "Modifier le retour", "dialog": {"title": "Ajouter un commentaire", "submit": "Soumettre un retour"}, "status": {"updating": "Mise à jour", "updated": "Retour mis à jour"}}}, "history": {"title": "Dernières entrées", "empty": "Tellement vide...", "show": "Afficher l'historique"}, "settings": {"title": "Panneau des paramètres"}, "watermark": "Développé avec"}, "threadHistory": {"sidebar": {"title": "Conversations passées", "filters": {"search": "<PERSON><PERSON><PERSON>", "placeholder": "Rechercher des conversations..."}, "timeframes": {"today": "<PERSON><PERSON><PERSON>'hui", "yesterday": "<PERSON>er", "previous7days": "7 derniers jours", "previous30days": "30 derniers jours"}, "empty": "Aucune conversation trouvée", "actions": {"close": "<PERSON><PERSON><PERSON> la barre latérale", "open": "<PERSON>u<PERSON><PERSON>r la barre latérale"}}, "thread": {"untitled": "Conversation sans titre", "menu": {"rename": "<PERSON>mmer", "delete": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"delete": {"title": "Confirmer la <PERSON>", "description": "Cela supprimera la conversation ainsi que ses messages et éléments. Cette action est irréversible", "success": "Conversation supprimée", "inProgress": "Suppression en cours"}, "rename": {"title": "Renommer la conversation", "description": "Entrez un nouveau nom pour cette conversation", "form": {"name": {"label": "Nom", "placeholder": "Entrez le nouveau nom"}}, "success": "Conversation renommée !", "inProgress": "Renommage en cours"}}}}, "navigation": {"header": {"chat": "Cha<PERSON>", "readme": "Lisez-moi", "theme": {"light": "Thème clair", "dark": "Thème sombre", "system": "Suivre le système"}}, "newChat": {"button": "Nouveau chat", "dialog": {"title": "<PERSON><PERSON>er un nouveau chat", "description": "<PERSON><PERSON> effacera l'historique de votre chat actuel. Voulez-vous vraiment continuer ?", "tooltip": "Nouveau chat"}}, "user": {"menu": {"settings": "Paramètres", "settingsKey": "S", "apiKeys": "Clés API", "logout": "Se déconnecter"}}}, "apiKeys": {"title": "Clés API requises", "description": "Pour utiliser cette application, les clés API suivantes sont requises. Les clés sont stockées dans le stockage local de votre appareil.", "success": {"saved": "Enregistré avec succès"}}, "alerts": {"info": "Info", "note": "Note", "tip": "Astuce", "important": "Important", "warning": "Avertissement", "caution": "Prudence", "debug": "Débogage", "example": "Exemple", "success": "Su<PERSON>ès", "help": "Aide", "idea": "<PERSON><PERSON><PERSON>", "pending": "En attente", "security": "Sécurité", "beta": "<PERSON><PERSON><PERSON>", "best-practice": "Bonne pratique"}}