import logging
import os
import sys

_log_level = os.environ.get("CHAINLIT_LOG_LEVEL", "INFO").upper()

logging.basicConfig(
    level=getattr(logging, _log_level, logging.INFO),
    stream=sys.stdout,
    format="%(asctime)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)

logging.getLogger("socketio").setLevel(logging.ERROR)
logging.getLogger("engineio").setLevel(logging.ERROR)
logging.getLogger("numexpr").setLevel(logging.ERROR)


logger = logging.getLogger("chainlit")
logger.setLevel(getattr(logging, _log_level, logging.INFO))
