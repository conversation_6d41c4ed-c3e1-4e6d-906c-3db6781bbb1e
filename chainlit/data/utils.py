import functools
from collections import deque
from typing import Optional

from chainlit.context import context
from chainlit.session import Web<PERSON><PERSON><PERSON>ession


def get_partner_from_session_token() -> Optional[str]:
    """Extract partner information from the current session (token first, then user metadata)"""
    try:
        print(f"[DEBUG] get_partner_from_session_token: context.session={context.session}")
        
        # 1) Prefer the token (most reliable, supports partner claim or scopes)
        if context.session and context.session.token:
            print(f"[DEBUG] get_partner_from_session_token: Found session with token")
            from chainlit.auth.jwt import FastApiSecuritySettings
            security = FastApiSecuritySettings()
            token_info = security.get_token_info(token=context.session.token)
            print(f"[DEBUG] get_partner_from_session_token: token_info={token_info}")
            
            partner = token_info.get("partner")
            if partner:
                print(f"[DEBUG] get_partner_from_session_token: found direct partner={partner}")
                return partner
                
            scopes = token_info.get("scopes", set())
            print(f"[<PERSON><PERSON>U<PERSON>] get_partner_from_session_token: scopes={scopes}")
            for scope in scopes:
                if scope.startswith("partner:"):
                    partner_from_scope = scope.split(":", 1)[1]
                    print(f"[DEBUG] get_partner_from_session_token: found partner from scope={partner_from_scope}")
                    return partner_from_scope
        else:
            print(f"[DEBUG] get_partner_from_session_token: No session or no token in session")
        
        # 2) Fallback to current session user metadata / scopes
        if context.session and context.session.user:
            user_meta = getattr(context.session.user, "metadata", {}) or {}
            print(f"[DEBUG] get_partner_from_session_token: user_metadata={user_meta}")
            scopes = user_meta.get("scopes")
            if isinstance(scopes, list):
                scopes = set(scopes)
            for scope in scopes or []:
                if scope.startswith("partner:"):
                    partner_from_user_scope = scope.split(":", 1)[1]
                    print(f"[DEBUG] get_partner_from_session_token: found partner from user scope={partner_from_user_scope}")
                    return partner_from_user_scope
        else:
            print(f"[DEBUG] get_partner_from_session_token: No session user")
        
        print(f"[DEBUG] get_partner_from_session_token: no partner found, returning None")
        return None
    except Exception as e:
        print(f"[DEBUG] get_partner_from_session_token: exception={e}")
        import traceback
        traceback.print_exc()
        return None


def get_partner_from_cookie(access_token: str) -> Optional[str]:
    """Extract partner information from an access token cookie"""
    try:
        print(f"[DEBUG] get_partner_from_cookie: processing token")
        from chainlit.auth.jwt import FastApiSecuritySettings
        security = FastApiSecuritySettings()
        token_info = security.get_token_info(token=access_token)
        print(f"[DEBUG] get_partner_from_cookie: token_info={token_info}")
        
        partner = token_info.get("partner")
        if partner:
            print(f"[DEBUG] get_partner_from_cookie: found direct partner={partner}")
            return partner
            
        scopes = token_info.get("scopes", set())
        print(f"[DEBUG] get_partner_from_cookie: scopes={scopes}")
        for scope in scopes:
            if scope.startswith("partner:"):
                partner_from_scope = scope.split(":", 1)[1]
                print(f"[DEBUG] get_partner_from_cookie: found partner from scope={partner_from_scope}")
                return partner_from_scope
        
        print(f"[DEBUG] get_partner_from_cookie: no partner found in token")
        return None
    except Exception as e:
        print(f"[DEBUG] get_partner_from_cookie: exception={e}")
        import traceback
        traceback.print_exc()
        return None





def queue_until_user_message():
    def decorator(method):
        @functools.wraps(method)
        async def wrapper(self, *args, **kwargs):
            if (
                isinstance(context.session, WebsocketSession)
                and not context.session.has_first_interaction
            ):
                # Queue the method invocation waiting for the first user message
                queues = context.session.thread_queues
                method_name = method.__name__
                if method_name not in queues:
                    queues[method_name] = deque()
                queues[method_name].append((method, self, args, kwargs))

            else:
                # Otherwise, Execute the method immediately
                return await method(self, *args, **kwargs)

        return wrapper

    return decorator
