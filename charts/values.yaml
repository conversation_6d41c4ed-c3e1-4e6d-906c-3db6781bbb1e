global:
  domain: energisme.net
  hostname: ai-services-dev-default
  apim:
    apim_tenant_id: e60a5bc7-4349-4909-b918-0750fc7ad1b7
    apim_subscription_id: 60faa8f0-9d6a-470f-97b0-32fc4593f530
    apim_rg_name: rg-gage-dev-weu-apim-service
    apim_name: apim-gage-dev-weu-service-f530
    apim_product_id: gage
  job:
    affinity: |
      nodeAffinity:
        requiredDuringSchedulingIgnoredDuringExecution:
          nodeSelectorTerms:
          - matchExpressions:
            - key: energisme.net/workload
              operator: In
              values:
              - application
# Default values for charts.
# This is a YAML-formatted file.
# Declare variables to be passed into your templates.

# This will set the replicaset count more information can be found here: https://kubernetes.io/docs/concepts/workloads/controllers/replicaset/
replicaCount: 1
revisionHistoryLimit: 1
# This sets the container image more information can be found here: https://kubernetes.io/docs/concepts/containers/images/
image:
  repository: acrdevopsvdc1.azurecr.io/datascience/docker/service/ai-agent
  # This sets the pull policy for images.
  pullPolicy: IfNotPresent
  # Overrides the image tag whose default is the chart appVersion.
  tag: "1.7.5"
# This is for the secretes for pulling an image from a private repository more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/pull-image-private-registry/
imagePullSecrets: []
# This is to override the chart name.
nameOverride: ""
fullnameOverride: ""
# This section builds out the service account more information can be found here: https://kubernetes.io/docs/concepts/security/service-accounts/
serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: {}
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: "ai-agent-sa"
# This is for setting Kubernetes Annotations to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/annotations/
podAnnotations: {}
# This is for setting Kubernetes Labels to a Pod.
# For more information checkout: https://kubernetes.io/docs/concepts/overview/working-with-objects/labels/
podLabels: {}
podSecurityContext: {}
# fsGroup: 2000

securityContext: {}
# capabilities:
#   drop:
#   - ALL
# readOnlyRootFilesystem: true
# runAsNonRoot: true
# runAsUser: 1000

# This is for setting up a service more information can be found here: https://kubernetes.io/docs/concepts/services-networking/service/
service:
  # This sets the service type more information can be found here: https://kubernetes.io/docs/concepts/services-networking/service/#publishing-services-service-types
  type: ClusterIP
  # This sets the ports more information can be found here: https://kubernetes.io/docs/concepts/services-networking/service/#field-spec-ports
  port: 80
# This block is for setting up the ingress for more information can be found here: https://kubernetes.io/docs/concepts/services-networking/ingress/
ingress:
  enabled: true
  className: ""
  annotations:
    kubernetes.io/ingress.class: traefik
    kubernetes.io/tls-acme: "true"
    cert-manager.io/cluster-issuer: letsencrypt-prod
    external-dns.alpha.kubernetes.io/hostname: ai-services-dev-default.energisme.net
    traefik.ingress.kubernetes.io/router.entrypoints: websecure
    traefik.ingress.kubernetes.io/router.tls: "true"
    # kubernetes.io/ingress.class: nginx
    # kubernetes.io/tls-acme: "true"
  hosts:
    - paths:
        - path: /ai-agent
          backend:
            serviceName: ai-agent
            servicePort: 80
      host: ai-services-dev-default.energisme.net
  tls:
    - secretName: ai-services-dev-default.energisme.net
      hosts:
        - ai-services-dev-default.energisme.net
        #  - secretName: chart-example-tls
        #    hosts:
        #      - chart-example.local
resources:
  requests:
    memory: 512Mi
  limits:
    memory: 512Mi
    # We usually recommend not to specify default resources and to leave this as a conscious
    # choice for the user. This also increases chances charts run on environments with little
    # resources, such as Minikube. If you do want to specify resources, uncomment the following
    # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
    # limits:
    #   cpu: 100m
    #   memory: 128Mi
    # requests:
    #   cpu: 100m
    #   memory: 128Mi
# This is to setup the liveness and readiness probes more information can be found here: https://kubernetes.io/docs/tasks/configure-pod-container/configure-liveness-readiness-startup-probes/
livenessProbe:
  httpGet:
    path: ai-agent/health
    port: http
readinessProbe:
  httpGet:
    path: ai-agent/health
    port: http
# This section is for setting up autoscaling more information can be found here: https://kubernetes.io/docs/concepts/workloads/autoscaling/
autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 100
  targetCPUUtilizationPercentage: 80
  # targetMemoryUtilizationPercentage: 80
# Additional volumes on the output Deployment definition.
volumes:
  - name: bs-ai-agent-inline
    csi:
      driver: secrets-store.csi.k8s.io
      readOnly: true
      volumeAttributes:
        secretProviderClass: azure-kvgagedevbackappai2f530-csi
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts:
  - name: bs-ai-agent-inline
    mountPath: "/mnt/bs-ai-agent"
    readOnly: true
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

# Additional env variables on the output Deployment definition.
env:
  - name: BACKEND_LEGACY_URL
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: backend_legacy_url
  - name: BACKEND_ECO_URL
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: backend_eco_url
  - name: PUBLIC_KEY_URL
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: public_key_url
  - name: API_DEFAULT_SIZE
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: api_default_size
  - name: API_TIMEOUT
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: api_timeout
  - name: DISABLE_FRONTEND
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: disable_frontend
  - name: POSTGRES_DB
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: postgres_db
  - name: POSTGRES_PORT
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: postgres_port
  - name: POSTGRES_HOST
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: postgres_host
  - name: POSTGRES_SCHEMA
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: postgres_schema
  - name: SANDBOX_API_URL
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: sandbox_api_url
  - name: LANGSMITH_TRACING
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: langsmith_tracing
  - name: LANGSMITH_ENDPOINT
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: langsmith_endpoint
  - name: LANGSMITH_PROJECT
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: langsmith_project
  - name: AZURE_DEEPSEEK_ENDPOINT
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: azure_deepseek_endpoint
  - name: AZURE_OPENAI_ENDPOINT
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: azure_openai_endpoint
  - name: AZURE_OPENAI_DEPLOYMENT_NAME
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: azure_openai_deployment_name
  - name: CHAINLIT_ROOT_PATH
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: chainlit_root_path
  - name: CHAINLIT_CUSTOM_AUTH
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: chainlit_custom_auth
  - name: CHAINLIT_OPENID_CONFIG_URL
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: chainlit_openid_config_url
  - name: CHAINLIT_JWT_ISSUER
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: chainlit_jwt_issuer
  - name: CHAINLIT_JWT_AUDIENCE
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: chainlit_jwt_audience
  - name: GENERIC_PARSER_API_URL
    valueFrom:
      configMapKeyRef:
        name: ai-agent-global-config
        key: generic_parser_api_url
  # Environment variables for secrets from Azure Key Vault
  - name: LANGSMITH_API_KEY
    valueFrom:
      secretKeyRef:
        name: bs-ai-agent
        key: langsmith-api-key
  - name: OPENAI_API_KEY
    valueFrom:
      secretKeyRef:
        name: bs-ai-agent
        key: openai-api-key
  - name: AZURE_DEEPSEEK_API_KEY
    valueFrom:
      secretKeyRef:
        name: bs-ai-agent
        key: azure-deepseek-api-key
  - name: AZURE_OPENAI_API_KEY
    valueFrom:
      secretKeyRef:
        name: bs-ai-agent
        key: azure-openai-api-key
  - name: POSTGRES_PASSWORD
    valueFrom:
      secretKeyRef:
        name: bs-ai-agent
        key: postgresql-bsaiagentupdater-password
  - name: POSTGRES_USER
    valueFrom:
      secretKeyRef:
        name: bs-ai-agent
        key: postgresql-bsaiagentupdater-user
nodeSelector: {}
tolerations: []
affinity:
  nodeAffinity:
    requiredDuringSchedulingIgnoredDuringExecution:
      nodeSelectorTerms:
        - matchExpressions:
            - key: energisme.net/workload
              operator: In
              values:
                - application
apim-registration:
  enabled: false
  api:
    ai-agent:
      schema_format: swagger
      schema_path: /openapi.json
backend:
  contextPath: "/ai-agent"
