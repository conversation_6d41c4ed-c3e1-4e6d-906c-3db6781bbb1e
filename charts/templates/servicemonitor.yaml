{{- if .Values.metrics }}
apiVersion: monitoring.coreos.com/v1
kind: ServiceMonitor
metadata:
  name: {{ include "ai-agent.fullname" . }}
  labels:
    {{- include "ai-agent.labels" . | nindent 4 }}
    prometheus.io/monitor: "true"
spec:
  selector:
    matchLabels:
      {{- include "ai-agent.selectorLabels" . | nindent 6 }}
  endpoints:
  - port: {{ .Values.ServiceMonitor.spec.endpoints.port }}
    targetPort: {{ .Values.ServiceMonitor.spec.endpoints.targetPort }}
    path: "{{ .Values.ServiceMonitor.spec.endpoints.path }}"
    honorLabels: true
{{- end }}
